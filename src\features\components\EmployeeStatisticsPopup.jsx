import { useState } from 'react';
import { Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend
} from 'chart.js';
import closeIcon from '../../assets/closeLoc.svg';
import prjIcon from '../../assets/folder.svg';
import folderIcon from '../../assets/folder.svg';
import rateIcon from '../../assets/clipboard-check.svg';
import taskIcon from '../../assets/briefcase.svg';
import calendarIcon from '../../assets/today.svg';
import '../../styles/EmployeeStatisticsPopup.css';

// Đăng ký các components Chart.js cần thiết
ChartJS.register(
  ArcElement,
  Tooltip,
  Legend
);

const EmployeeStatisticsPopup = ({ employee: user, onClose }) => {
  const [activeTab, setActiveTab] = useState('task-analysis');

  // Sử dụng dữ liệu thực từ user.statusData hoặc fallback data
  const getUserStatusData = () => {
    if (user?.statusData) {
      return user.statusData;
    }

    // Fallback data nếu không có statusData
    return {
      labels: ['Xem xét', 'Hoàn thành đúng hạn', 'Hoàn thành muộn', 'Quá hạn', 'Hoàn thành sớm', 'Đang chờ', 'Đang triển khai'],
      data: [0, 0, 0, 0, 0, 0, 0],
      colors: ['#BA68C8', '#26C6DA', '#FFB74D', '#EF5350', '#66BB6A', '#B0BEC5', '#42A5F5']
    };
  };

  const statusData = getUserStatusData();

  const chartData = {
    labels: statusData.labels,
    datasets: [
      {
        data: statusData.data,
        backgroundColor: statusData.colors,
        borderWidth: 0,
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    rotation: 160,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
        backgroundColor: '#fff',
        titleColor: '#333',
        bodyColor: '#666',
        borderColor: '#e0e0e0',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function(context) {
            return `${context.parsed}`;
          }
        }
      },
    },
    elements: {
      arc: {
        borderWidth: 1,
      }
    }
  };

  // Legend Component
  const StatusLegend = () => {
    // Tạo legend items từ dữ liệu thực
    const legendItems = statusData.labels.map((label, index) => ({
      label: label,
      data: statusData.data[index],
      color: statusData.colors[index]
    }));

    // Sắp xếp legend theo thứ tự cụ thể
    const getOrderedItems = () => {
      const orderMap = {
        'Hoàn thành sớm': { order: 0, column: 1 },
        'Hoàn thành muộn': { order: 1, column: 1 },
        'Hoàn thành đúng hạn': { order: 2, column: 1 },
        'Đang chờ': { order: 0, column: 2 },
        'Đang triển khai': { order: 1, column: 2 },
        'Xem xét': { order: 0, column: 3 },
        'Quá hạn': { order: 1, column: 3 }
      };

      const column1 = [];
      const column2 = [];
      const column3 = [];

      legendItems.forEach(item => {
        const orderInfo = orderMap[item.label];
        if (orderInfo) {
          if (orderInfo.column === 1) column1.push({ ...item, order: orderInfo.order });
          else if (orderInfo.column === 2) column2.push({ ...item, order: orderInfo.order });
          else if (orderInfo.column === 3) column3.push({ ...item, order: orderInfo.order });
        }
      });

      // Sắp xếp theo thứ tự trong mỗi cột
      column1.sort((a, b) => a.order - b.order);
      column2.sort((a, b) => a.order - b.order);
      column3.sort((a, b) => a.order - b.order);

      return { column1, column2, column3 };
    };

    const { column1, column2, column3 } = getOrderedItems();

    const renderLegendColumn = (items) => (
      <div className="employee-statistics-legend-column">
        {items.map((item, index) => (
          <div key={`${item.label}-${index}`} className="employee-statistics-legend-item">
            <div
              className="employee-statistics-legend-color"
              style={{ backgroundColor: item.color }}
            >
              {item.data}
            </div>
            <span className="employee-statistics-legend-label">
              {item.label}
            </span>
          </div>
        ))}
      </div>
    );

    return (
      <div className="employee-statistics-legend">
        <div className="employee-statistics-legend-title">Tổng quan</div>
        <div className="employee-statistics-legend-grid">
          {renderLegendColumn(column1)}
          {renderLegendColumn(column2)}
          {renderLegendColumn(column3)}
        </div>
      </div>
    );
  };

  return (
    <div className="employee-statistics-overlay" onClick={onClose}>
      <div className="employee-statistics-popup" onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="employee-statistics-header">
          <div className="employee-statistics-avatar-section">
            <div className="employee-statistics-avatar">
              <img src={user?.avatar || "https://randomuser.me/api/portraits/men/1.jpg"} alt="avatar" />
            </div>
            <div className="employee-statistics-info">
              <h2 className="employee-statistics-name">{user?.name || 'Nguyễn Hoài Gia Bảo'}</h2>
              <p className="employee-statistics-position">{user?.position || 'BA phòng IT'}</p>
            </div>
          </div>
          <button className="employee-statistics-close-btn" onClick={onClose}>
            <img src={closeIcon} alt="close" />
          </button>
        </div>

                 {/* Key Metrics */}
         <div className="employee-statistics-metrics">
           <div className="employee-statistics-metric-card">
             <div className="employee-statistics-metric-header">
               <div className="employee-statistics-metric-icon">
                 <img src={prjIcon} alt="projects" />
               </div>
               <div className="employee-statistics-metric-label">Tổng số dự án</div>
             </div>
             <div className="employee-statistics-metric-value">{user?.totalProjects || 0}</div>
           </div>

           <div className="employee-statistics-metric-card">
             <div className="employee-statistics-metric-header">
               <div className="employee-statistics-metric-icon">
                 <img src={taskIcon} alt="tasks" />
               </div>
               <div className="employee-statistics-metric-label">Tổng số công việc</div>
             </div>
             <div className="employee-statistics-metric-value">{user?.totalTasks || 0}</div>
           </div>

           <div className="employee-statistics-metric-card">
             <div className="employee-statistics-metric-header">
               <div className="employee-statistics-metric-icon">
                 <img src={rateIcon} alt="completion" />
               </div>
               <div className="employee-statistics-metric-label">Tỉ lệ hoàn thành</div>
             </div>
             <div className="employee-statistics-metric-value">{user?.completionRate || 0}%</div>
           </div>

           <div className="employee-statistics-metric-card">
             <div className="employee-statistics-metric-header">
               <div className="employee-statistics-metric-icon">
                 <img src={calendarIcon} alt="join date" />
               </div>
               <div className="employee-statistics-metric-label">Ngày tham gia</div>
             </div>
             <div className="employee-statistics-metric-value">{user?.createdAt || ''}</div>
           </div>
         </div>

        {/* Tabs */}
        <div className="employee-statistics-tabs">
          <button
            className={`employee-statistics-tab ${activeTab === 'task-analysis' ? 'active' : ''}`}
            onClick={() => setActiveTab('task-analysis')}
          >
            Phân tích công việc
          </button>
          <button
            className={`employee-statistics-tab ${activeTab === 'projects' ? 'active' : ''}`}
            onClick={() => setActiveTab('projects')}
          >
            Dự án
          </button>
        </div>

        {/* Content */}
        <div className="employee-statistics-content">
          {activeTab === 'task-analysis' && (
            <div className="employee-statistics-task-analysis">
              <div className="employee-statistics-chart-section">
                <div className="employee-statistics-section-title">
                  Phân bổ trạng thái công việc
                  {/* <div className="employee-statistics-section-subtitle">
                    Phân bổ trạng thái công việc
                  </div> */}
                </div>
                
                <div className="employee-statistics-chart-content">
                  <div className="employee-statistics-pie-chart">
                    {statusData.data.some(value => value > 0) ? (
                      <Pie data={chartData} options={chartOptions} />
                    ) : (
                      <div style={{
                        width: '200px',
                        height: '200px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        background: '#D1D5DB',
                        borderRadius: '50%',
                        color: '#9CA3AF',
                        fontSize: '14px',
                        textAlign: 'center',
                        fontWeight: '600'
                      }}
                      title="Chưa phân việc"
                      >
                        <div>Chưa phân việc</div>
                      </div>
                    )}
                  </div>
                  <div className="employee-statistics-legend-container">
                    <StatusLegend />
                  </div>
                </div>
              </div>
            </div>
          )}

                     {activeTab === 'projects' && (
             <div className="employee-statistics-projects">
               <div className="employee-statistics-section-title">
                 Danh sách dự án
               </div>
               <div className="employee-statistics-projects-list">
                 {user?.projects && user.projects.length > 0 ? (
                   user.projects.map((project, index) => (
                     <div key={project.id || index} className="employee-statistics-project-item">
                       <div className="employee-statistics-project-icon">
                         <img src={folderIcon} alt="project" />
                       </div>
                       <div className="employee-statistics-project-name">
                         {project.name || project.title || `Dự án ${index + 1}`}
                       </div>
                     </div>
                   ))
                 ) : (
                   <div style={{
                     textAlign: 'center',
                     padding: '40px 20px',
                     color: '#666'
                   }}>
                     <div style={{ fontSize: '48px', marginBottom: '16px', opacity: '0.3' }}>📁</div>
                     <div style={{ marginBottom: '8px', fontWeight: '500' }}>
                       Chưa tham gia dự án nào
                     </div>
                     <div style={{ fontSize: '14px' }}>
                       Nhân viên này chưa được phân công vào dự án nào
                     </div>
                   </div>
                 )}
               </div>
             </div>
           )}
        </div>
      </div>
    </div>
  );
};

export default EmployeeStatisticsPopup; 