import { toast } from 'react-toastify';
import { useEffect } from 'react';

// Hàm tiện ích để gọi toast từng loại cụ thể, tránh spam bằng cách kiểm tra toast đang hiển thị
const toastOptions = {
  toastId: 'global-toast',
  autoClose: 3000, // Tự động đóng sau 3 giây
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true
};

export const showSuccess = (message) => toast.success(message, toastOptions);
export const showError = (message) => toast.error(message, toastOptions);
export const showWarning = (message) => toast.warn(message, toastOptions);
export const showInfo = (message) => toast.info(message, toastOptions);

// Hàm tiện ích để gọi toast theo từng loại
export const showToast = (message, type = 'info') => {
  switch (type) {
    case 'success':
      showSuccess(message);
      break;
    case 'error':
      showError(message);
      break;
    case 'warning':
      showWarning(message);
      break;
    default:
      showInfo(message);
  }
};

// Utility functions để quản lý các sự kiện cập nhật task
export const triggerTaskUpdate = (eventType = 'projectTasksUpdated') => {
  window.dispatchEvent(new Event(eventType));
};

export const triggerTaskEvents = (events = ['projectTasksUpdated']) => {
  events.forEach(eventType => {
    window.dispatchEvent(new Event(eventType));
  });
};

// Các loại sự kiện cập nhật task
export const TASK_EVENTS = {
  CREATED: 'taskCreated',
  UPDATED: 'taskUpdated', 
  STATUS_UPDATED: 'taskStatusUpdated',
  DELETED: 'taskDeleted',
  PROJECT_TASKS_UPDATED: 'projectTasksUpdated',
  PERSONAL_TASKS_UPDATED: 'personalTasksUpdated'
};

// Custom hook để lắng nghe sự kiện cập nhật task
export const useTaskUpdateListener = (callback, eventTypes = [TASK_EVENTS.PROJECT_TASKS_UPDATED]) => {
  useEffect(() => {
    const handleTaskUpdate = () => {
      if (callback) {
        callback();
      }
    };

    eventTypes.forEach(eventType => {
      window.addEventListener(eventType, handleTaskUpdate);
    });

    return () => {
      eventTypes.forEach(eventType => {
        window.removeEventListener(eventType, handleTaskUpdate);
      });
    };
  }, [callback, eventTypes]);
};
