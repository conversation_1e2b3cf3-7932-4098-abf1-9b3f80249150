// Thêm cache và preload ở đầu file
let myJobCache = null;
let myJobCacheTimestamp = 0;
const MYJOB_CACHE_DURATION = 10000; // 10s
let myJobPreloadPromise = null;

import React, { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import {
  getMyProjectTasks,
  getProjectTasks,
  updateProjectTask,
} from "../../../api/taskManagement";
import { getAllProjects } from "../../../api/projectManagement";
import searchIcon from "../../../assets/search.svg";
import jobIcon from "../../../assets/icon-sidebar/congviec.svg";
import waitingIcon from "../../../assets/waiting.svg";
import deploymentIcon from "../../../assets/deployment.svg";
import completeIcon from "../../../assets/complete.svg";
import considerIcon from "../../../assets/consider.svg";
import overdueIcon from "../../../assets/triangle-alert.svg";
import highPriorityIcon from "../../../assets/High.svg";
import mediumPriorityIcon from "../../../assets/Medium.svg";
import normalPriorityIcon from "../../../assets/Normal.svg";
import "../../../styles/MyJob.css";
// Removed Sidebar and Topbar imports as they're now handled by DashboardLayout
import DetailJob from "../../components/JobDetail";
import NoJobImg from "../../../assets/NoJob.svg";

const preloadMyJobs = async () => {
  if (myJobPreloadPromise) return myJobPreloadPromise;
  myJobPreloadPromise = (async () => {
    try {
      const response = await getMyProjectTasks({
        noLimit: true,
        limit: 1000,
        sort: '-createdAt',
        populate: 'assignedTo,assignedToMultiple,createdBy,projectId'
      });
      const tasks = response.data || response;
      if (!Array.isArray(tasks)) return [];
      const userRaw = JSON.parse(localStorage.getItem('user') || '{}');
      const currentUserId = userRaw._id || userRaw.id || (userRaw.user && (userRaw.user._id || userRaw.user.id));
      const transformedTasks = tasks.map((task, index) => ({
        id: task._id || task.id,
        code: task.taskCode || `TASK-${index + 1}`,
        name: `${task.taskCode || `TASK-${index + 1}`} ${task.title || task.name || "Chưa có tên"}`,
        project: task.projectId?.name || task.projectId?.projectCode || "Chưa xác định",
        projectId: task.projectId?._id || task.projectId?.id || task.projectId,
        status: task.status || "pending",
        priority: task.priority || "medium",
        dueDate: task.dueDate ? new Date(task.dueDate).toLocaleDateString("vi-VN") : "",
        startDate: task.startDate ? new Date(task.startDate).toLocaleDateString("vi-VN") : "",
        creator: {
          name: task.createdBy?.fullName || "Không xác định",
          avatar: task.createdBy?.avatar || "",
        },
        assignee: task.assignedToId ? [{ name: task.assignedToId.fullName || "Không xác định", avatar: task.assignedToId.avatar || "" }] : [],
        description: task.description || "",
        progress: task.progress || 0,
        activities: [],
        attachments: [],
      }));
      myJobCache = transformedTasks;
      myJobCacheTimestamp = Date.now();
      return transformedTasks;
    } catch {
      return [];
    }
  })();
  return myJobPreloadPromise;
};

const PAGE_SIZE = 10;

const MyJob = () => {
  const [jobs, setJobs] = useState(myJobCache || []);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(!myJobCache);
  const [currentPage, setCurrentPage] = useState(1);
  const [showStatusPopup, setShowStatusPopup] = useState(false);
  const [popupPosition, setPopupPosition] = useState({ top: 0, left: 0 });
  const [activeJobId, setActiveJobId] = useState(null);
  const [hoveredStatus, setHoveredStatus] = useState(null);
  const [changingJobId, setChangingJobId] = useState(null);
  const [selectedTask, setSelectedTask] = useState(null);
  // State hiển thị popup đổi mức độ ưu tiên
  const [showPriorityPopup, setShowPriorityPopup] = useState(false);
  // Vị trí popup mức độ ưu tiên
  const [priorityPopupPosition, setPriorityPopupPosition] = useState({
    top: 0,
    left: 0,
  });
  // Lưu mức độ ưu tiên đang hover
  const [hoveredPriority, setHoveredPriority] = useState(null);
  const [myProjectIds, setMyProjectIds] = useState([]);

  const popupRef = useRef(null);
  const statusCellRef = useRef(null);
  const priorityPopupRef = useRef(null);
  const priorityCellRef = useRef(null);

  // Load jobs on component mount
  useEffect(() => {
    // Lấy danh sách projectId mà user là thành viên
    const fetchMyProjects = async () => {
      try {
        const userRaw = JSON.parse(localStorage.getItem('user') || '{}');
        const currentUserId = userRaw._id || userRaw.id || (userRaw.user && (userRaw.user._id || userRaw.user.id));
        const response = await getAllProjects();
        const projects = response.data || response;
        // All projects loaded
        projects.forEach(project => {
                      // Project members loaded
        });
        const myIds = projects
          .filter(project => Array.isArray(project.members) && project.members.some(m => m.userId === currentUserId || m._id === currentUserId))
          .map(project => project._id || project.id);
        setMyProjectIds(myIds);
      } catch (e) {
        setMyProjectIds([]);
      }
    };
    fetchMyProjects();
  }, []);

  useEffect(() => {
    let ignore = false;
    const loadMyProjectTasks = async () => {
      const now = Date.now();
      if (myJobCache && (now - myJobCacheTimestamp) < MYJOB_CACHE_DURATION) {
        setJobs(myJobCache);
        setLoading(false);
        preloadMyJobs().then(newJobs => {
          if (!ignore && newJobs) {
            setJobs(newJobs);
            setLoading(false);
          }
        });
        return;
      }
      setLoading(!myJobCache);
      try {
        const newJobs = await preloadMyJobs();
        if (!ignore && newJobs) {
          setJobs(newJobs);
          setLoading(false);
        }
      } catch {
        if (!ignore) {
          setJobs([]);
          setLoading(false);
        }
      }
    };
    loadMyProjectTasks();
    return () => { ignore = true; };
  }, [myProjectIds]);


  // Disable page scroll for this component
  useEffect(() => {
    const dashboardMain = document.querySelector('.dashboard-main');
    if (dashboardMain) {
      dashboardMain.classList.add('no-page-scroll');
    }

    return () => {
      if (dashboardMain) {
        dashboardMain.classList.remove('no-page-scroll');
      }
    };
  }, []);

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Handle row click to open job detail
  const handleRowClick = (job) => {
    setSelectedTask(job);
  };

  // Get status icon based on status
  const getStatusIcon = (status) => {
    switch (status) {
      case "in_progress":
      case "Đang tiến hành":
        return deploymentIcon;
      case "completed":
      case "Hoàn thành":
        return completeIcon;
      case "pending":
      case "Đang chờ":
        return waitingIcon;
      case "overdue":
      case "Quá hạn":
        return overdueIcon;
      case "review":
      case "Xem xét":
        return considerIcon;
      default:
        return waitingIcon;
    }
  };

  // Get priority icon based on priority
  const getPriorityIcon = (priority) => {
    const p = String(priority).toLowerCase();
    switch (p) {
      case "high":
      case "cao":
        return highPriorityIcon; // đỏ
      case "medium":
      case "trung bình":
      case "trung binh":
        return mediumPriorityIcon; // cam
      case "low":
      case "thấp":
      case "thap":
        return normalPriorityIcon; // xanh lá
      default:
        return normalPriorityIcon;
    }
  };

  const getPriorityText = (priority) => {
    if (!priority) return "Chưa xác định";
    const p = String(priority).toLowerCase();
    switch (p) {
      case "high":
      case "cao":
        return "Cao";
      case "medium":
      case "trung bình":
      case "trung binh":
        return "Trung bình";
      case "low":
      case "thấp":
      case "thap":
        return "Thấp";
      default:
        if (["Cao", "Trung bình", "Thấp"].includes(priority)) return priority;
        return "Thấp";
    }
  };

  // Get status text
  const getStatusText = (status) => {
    switch (status) {
      case "pending":
        return "Đang chờ";
      case "in_progress":
        return "Đang tiến hành";
      case "completed":
        return "Hoàn thành";
      case "overdue":
        return "Quá hạn";
      case "review":
        return "Xem xét";
      // Trường hợp status đã là text tiếng Việt
      case "Đang chờ":
      case "Đang tiến hành":
      case "Hoàn thành":
      case "Quá hạn":
      case "Xem xét":
        return status;
      default:
        return status;
    }
  };

  // Handle status change
  const handleStatusChange = async (jobId, newStatus) => {
    setChangingJobId(jobId);

    try {
      // Tìm job để lấy projectId
      const job = jobs.find((j) => j.id === jobId);
      if (!job) {
        console.error("Không tìm thấy công việc");
        return;
      }

      // Gọi API để cập nhật status
      await updateProjectTask(job.projectId, jobId, { status: newStatus });

      // Cập nhật local state sau khi API thành công
      const updatedJobs = jobs.map((job) =>
        job.id === jobId
          ? {
              ...job,
              status: newStatus,
              statusColor:
                newStatus === "completed"
                  ? "#52c41a"
                  : newStatus === "overdue"
                  ? "#ff4d4f"
                  : newStatus === "review"
                  ? "#722ed1"
                  : newStatus === "in_progress"
                  ? "#1890ff"
                  : "#8c8c8c",
            }
          : job
      );
      setJobs(updatedJobs);
      setShowStatusPopup(false);
    } catch (error) {
      console.error("Lỗi khi cập nhật trạng thái:", error);
      // Có thể hiển thị thông báo lỗi cho user
    } finally {
      setChangingJobId(null);
    }
  };

  // Handle priority change
  const handlePriorityChange = async (jobId, newPriority) => {
    // Find job to get projectId
    const job = jobs.find((j) => j.id === jobId);
    if (!job) {
      console.error("Không tìm thấy công việc");
      return;
    }
    try {
      // Call API to update priority
      await updateProjectTask(job.projectId, jobId, { priority: newPriority });
      // Update local state after API success
      const updatedJobs = jobs.map((job) =>
        job.id === jobId
          ? {
              ...job,
              priority: newPriority,
            }
          : job
      );
      setJobs(updatedJobs);
      setShowPriorityPopup(false);
    } catch (error) {
      console.error("Lỗi khi cập nhật mức độ ưu tiên:", error);
      // Optionally show error to user
    }
  };

  // Handle status hover
  const handleStatusHover = (e, job) => {
    statusCellRef.current = e.currentTarget;
    const rect = e.currentTarget.getBoundingClientRect();

    setPopupPosition({
      top: rect.bottom,
      left: rect.left,
    });

    setActiveJobId(job.id);
    // Xử lý cả trường hợp status là key (waiting, in-progress...) và text (Chờ duyệt, Đang tiến hành...)
    let statusKey = job.status;
    if (job.status === "Chờ duyệt") statusKey = "waiting";
    else if (job.status === "Đang tiến hành") statusKey = "in-progress";
    else if (job.status === "Hoàn thành") statusKey = "completed";
    else if (job.status === "Xem xét") statusKey = "review";

    setHoveredStatus(statusKey);
    setShowStatusPopup(true);
  };

  // Handle mouse leave
  const handleMouseLeave = (e) => {
    const toElement = e.relatedTarget;
    if (popupRef.current && popupRef.current.contains(toElement)) {
      return;
    }

    setTimeout(() => {
      if (
        popupRef.current &&
        !popupRef.current.matches(":hover") &&
        statusCellRef.current &&
        !statusCellRef.current.matches(":hover")
      ) {
        setShowStatusPopup(false);
      }
    }, 100);
  };

  // Handle priority hover
  const handlePriorityHover = (e, job) => {
    priorityCellRef.current = e.currentTarget;
    const rect = e.currentTarget.getBoundingClientRect();

    setPriorityPopupPosition({
      top: rect.bottom,
      left: rect.left,
    });

    setActiveJobId(job.id);
    // Xử lý cả trường hợp priority là key (high, medium, normal) và text (Cao, Trung bình, Bình thường)
    let priorityKey = job.priority;
    if (job.priority === "Cao") priorityKey = "high";
    else if (job.priority === "Trung bình") priorityKey = "medium";
    else if (job.priority === "Bình thường") priorityKey = "normal";

    setHoveredPriority(priorityKey);
    setShowPriorityPopup(true);
  };

  // Handle priority mouse leave
  const handlePriorityMouseLeave = (e) => {
    const toElement = e.relatedTarget;
    if (
      priorityPopupRef.current &&
      priorityPopupRef.current.contains(toElement)
    ) {
      return;
    }

    setTimeout(() => {
      if (
        priorityPopupRef.current &&
        !priorityPopupRef.current.matches(":hover") &&
        priorityCellRef.current &&
        !priorityCellRef.current.matches(":hover")
      ) {
        setShowPriorityPopup(false);
      }
    }, 100);
  };

  // Status popup component
  const StatusPopup = () => {
    if (!showStatusPopup) return null;

    const statuses = [
      { value: "pending", label: "Đang chờ", icon: waitingIcon },
      { value: "in_progress", label: "Đang tiến hành", icon: deploymentIcon },
      { value: "completed", label: "Hoàn thành", icon: completeIcon },
      { value: "overdue", label: "Quá hạn", icon: overdueIcon },
      { value: "review", label: "Xem xét", icon: considerIcon },
    ];

    return (
      <div
        className="status-popup"
        style={{
          top: `${popupPosition.top}px`,
          left: `${popupPosition.left}px`,
          pointerEvents: "auto",
        }}
        ref={popupRef}
        onMouseLeave={handleMouseLeave}
      >
        <div className="status-popup-header">Trạng thái</div>
        <div className="status-popup-options">
          {statuses.map((status) => (
            <div
              key={status.value}
              className={`status-option ${
                hoveredStatus === status.value ? "active" : ""
              }`}
              onClick={() => handleStatusChange(activeJobId, status.value)}
            >
              <img src={status.icon} alt="" className="status-icon" />
              <span>{status.label}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Priority popup component
  const PriorityPopup = () => {
    if (!showPriorityPopup) return null;

    const priorities = [
      { value: "low", label: "Thấp", icon: normalPriorityIcon },
      { value: "medium", label: "Trung bình", icon: mediumPriorityIcon },
      { value: "high", label: "Cao", icon: highPriorityIcon },
    ];

    return (
      <div
        className="priority-popup"
        style={{
          top: `${priorityPopupPosition.top}px`,
          left: `${priorityPopupPosition.left}px`,
          pointerEvents: "auto",
        }}
        ref={priorityPopupRef}
        onMouseLeave={handlePriorityMouseLeave}
      >
        <div className="priority-popup-header">Mức độ</div>
        <div className="priority-popup-options">
          {priorities.map((priority) => (
            <div
              key={priority.value}
              className={`priority-option ${
                hoveredPriority === priority.value ? "active" : ""
              }`}
              onClick={() => handlePriorityChange(activeJobId, priority.value)}
            >
              <img src={priority.icon} alt="" className="priority-icon" />
              <span>{priority.label}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Filter jobs by search term
  const filteredJobs = jobs.filter((job) => {
    if (!searchTerm.trim()) return true;

    const searchLower = searchTerm.toLowerCase().trim();

    return (
      job.name.toLowerCase().includes(searchLower) ||
      job.id.toLowerCase().includes(searchLower) ||
      job.project.toLowerCase().includes(searchLower) ||
      getStatusText(job.status).toLowerCase().includes(searchLower) ||
      getPriorityText(job.priority).toLowerCase().includes(searchLower) ||
      job.creator.name.toLowerCase().includes(searchLower) ||
      (job.assignee &&
        job.assignee.some((assignee) =>
          assignee.name.toLowerCase().includes(searchLower)
        )) ||
      (job.description && job.description.toLowerCase().includes(searchLower))
    );
  });

  // Pagination calculations
  const totalPages = Math.max(1, Math.ceil(filteredJobs.length / PAGE_SIZE));
  useEffect(() => {
    if (currentPage > totalPages) {
      setCurrentPage(totalPages);
    }
  }, [totalPages]);
  const startIndex = (currentPage - 1) * PAGE_SIZE;
  const endIndex = startIndex + PAGE_SIZE;
  const currentPageJobs = filteredJobs.slice(startIndex, endIndex);

  return (
    <div className="my-job-container">
          <div className="page-header">
            <img src={jobIcon} alt="Job Icon" className="job-icon" />
            <h1 className="jobs-title">Công việc của tôi</h1>
          </div>

          <div className="job-actions">
            <div className="list-label">Danh sách</div>
            <div className="search-container">
              <img src={searchIcon} alt="Search" className="searc-icon" />
              <input
                type="text"
                placeholder="Tìm kiếm"
                value={searchTerm}
                onChange={handleSearchChange}
                className="job-search-input"
              />
            </div>
          </div>

          <div className="job-list">
            {loading ? (
              <table className="job-table">
                <thead>
                  <tr>
                    <th>Tên công việc</th>
                    <th>Tên dự án</th>
                    <th>Trạng thái</th>
                    <th>Mức độ</th>
                    <th>Ngày kết thúc</th>
                    <th>Người giao việc</th>
                  </tr>
                </thead>
                <tbody>
                  {Array.from({ length: 6 }).map((_, idx) => (
                    <tr key={idx} style={{ opacity: 0.7 }}>
                      <td><div style={{ width: 120, height: 16, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                      <td><div style={{ width: 100, height: 16, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                      <td><div style={{ width: 80, height: 16, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                      <td><div style={{ width: 60, height: 16, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                      <td><div style={{ width: 90, height: 16, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                      <td><div style={{ width: 40, height: 16, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : filteredJobs.length > 0 ? (
              <>
              <table className="job-table">
                <thead>
                  <tr>
                    <th>Tên công việc</th>
                    <th>Tên dự án</th>
                    <th>Trạng thái</th>
                    <th>Mức độ</th>
                    <th>Ngày kết thúc</th>
                    <th>Người giao việc</th>
                  </tr>
                </thead>
                <tbody>
                  {currentPageJobs.map((job, index) => {
                    // Map priority to class name - xử lý cả tiếng Anh và tiếng Việt
                    const priorityClass =
                      job.priority === "high" || job.priority === "Cao"
                        ? "high"
                        : job.priority === "medium" ||
                          job.priority === "Trung bình"
                        ? "medium"
                        : "normal";

                    return (
                      <tr
                        key={job.id || index}
                        onClick={() => handleRowClick(job)}
                      >
                        <td>
                          <div className="job-name">
                            <span className="job-title">{job.name}</span>
                          </div>
                        </td>
                        <td>{job.project}</td>
                        <td className="task-status-cell">
                          <div
                            className={`task-status ${
                              // Xử lý cả trường hợp status là key và text
                              job.status === "pending" ||
                              job.status === "Đang chờ"
                                ? "pending"
                                : job.status === "in_progress" ||
                                  job.status === "Đang tiến hành"
                                ? "in-progress"
                                : job.status === "completed" ||
                                  job.status === "Hoàn thành"
                                ? "completed"
                                : job.status === "overdue" ||
                                  job.status === "Quá hạn"
                                ? "overdue"
                                : job.status === "review" ||
                                  job.status === "Xem xét"
                                ? "review"
                                : "pending"
                            } ${
                              changingJobId === job.id ? "status-changing" : ""
                            }`}
                            onMouseEnter={(e) => handleStatusHover(e, job)}
                            onMouseLeave={handleMouseLeave}
                          >
                            <img
                              src={getStatusIcon(job.status)}
                              alt=""
                              className="status-icon"
                            />
                            <span className="status-text">
                              {getStatusText(job.status)}
                            </span>
                          </div>
                        </td>
                        <td className="task-priority-cell">
                          <div
                            className={`task-priority ${priorityClass}`}
                            onMouseEnter={(e) => handlePriorityHover(e, job)}
                            onMouseLeave={handlePriorityMouseLeave}
                          >
                            <img
                              src={getPriorityIcon(priorityClass)}
                              alt=""
                              className="priority-icon"
                            />
                            <span className="priority-text">
                              {getPriorityText(job.priority)}
                            </span>
                          </div>
                        </td>
                        <td>{job.dueDate}</td>
                        <td className="assignee-cell">
                          <img
                            src={job.creator.avatar || "/default-avatar.png"}
                            alt={job.creator.name}
                            className="avatar"
                            title={`Người giao việc: ${job.creator.name}`}
                          />
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
              <div className="pagination-controls" style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: 12, padding: '12px 4px' }}>
                <button
                  onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                  disabled={currentPage <= 1}
                  style={{ padding: '6px 10px', borderRadius: 6, border: '1px solid #ddd', background: currentPage <= 1 ? '#f5f5f5' : '#fff', cursor: currentPage <= 1 ? 'not-allowed' : 'pointer' }}
                >
                  Trước
                </button>
                <span style={{ color: '#555' }}>Trang {currentPage}/{totalPages}</span>
                <button
                  onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
                  disabled={currentPage >= totalPages}
                  style={{ padding: '6px 10px', borderRadius: 6, border: '1px solid #ddd', background: currentPage >= totalPages ? '#f5f5f5' : '#fff', cursor: currentPage >= totalPages ? 'not-allowed' : 'pointer' }}
                >
                  Sau
                </button>
              </div>
              </>
            ) : jobs.length === 0 ? (
              <div className="empty-state" style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                height: "60vh",
                color: "#ccc"
              }}>
                <img src={NoJobImg} alt="No jobs" style={{ width: 600, marginBottom: 16 }} />
              </div>
            ) : (
              <div className="empty-state">
                <p>
                  Không tìm thấy công việc nào phù hợp với từ khóa tìm kiếm.
                </p>
              </div>
            )}
          </div>

          {/* Container for UI elements like popups */}
          <div className="ui-elements-container">
            <StatusPopup />
            <PriorityPopup />
          </div>

          {/* Job Detail Modal */}
          {selectedTask && (
            <DetailJob
              task={selectedTask}
              onClose={() => setSelectedTask(null)}
              hideExport={true}
            />
          )}
        </div>
  );
};

export default MyJob;