// Thêm cache và preload ở đầu file
let timelineCache = {};
let timelineCacheTimestamp = {};
const TIMELINE_CACHE_DURATION = 10000; // 10 giây
let timelinePreloadPromiseMap = {};

import React, { useState, useRef, useEffect } from 'react';
// Import hook useOutletContext để lấy context từ component cha (thường dùng trong react-router-dom)
import { useOutletContext, useParams } from 'react-router-dom';
import '../../../styles/Timeline.css';
import todayIcon from '../../../assets/today.svg';
import userAvatar from '../../../assets/user1.png';
import DetailJob from '../../components/JobDetail';
import { getProjectMembers, transformUserData } from '../../../api/userManagement';
import { getProjectTasks, transformTaskListData, updateProjectTask } from '../../../api/taskManagement';

const Timeline = () => {
  // Lấy context từ component cha (WorkContent) nếu có, nếu không thì trả về object rỗng
  const context = useOutletContext() || {};
  // Lấy ra hai biến sortOption (tuỳ chọn sắp xếp) và filterOptions (tuỳ chọn lọc) từ context
  const { sortBy: sortOption, filters: filterOptions } = context;

  const { projectId } = useParams();

  // Loading and error states
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Members and tasks from API
  const [teamMembers, setTeamMembers] = useState([]);
  const [tasks, setTasks] = useState([]);

  // State for selected task to show in detail view
  const [selectedTask, setSelectedTask] = useState(null);

  // Current date state
  const today = new Date();
  const [currentDate, setCurrentDate] = useState(new Date()); // Use current real time
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const [isYearSelectOpen, setIsYearSelectOpen] = useState(false);
  const [isMonthSelectOpen, setIsMonthSelectOpen] = useState(false);
  const datePickerRef = useRef(null);
  const yearSelectRef = useRef(null);
  const monthSelectRef = useRef(null);

  // Sample team members data (would come from API in real implementation)
  // const [teamMembers] = useState([
  //   { id: 1, name: 'Team phát triển', type: 'team', color: '#91C483' },
  //   { id: 2, name: 'Tấn Sanh', role: 'Back-end', avatar: userAvatar, color: '#FFA500' },
  //   { id: 3, name: 'Hoài Nam', role: 'Back-end', avatar: userAvatar, color: '#FFA500' },
  //   { id: 4, name: 'Huỳnh', role: 'Front-end', avatar: userAvatar, color: '#FFA500' },
  //   { id: 5, name: 'Nhân', role: 'Front-end', avatar: userAvatar, color: '#FFA500' },
  //   { id: 6, name: 'Nguyễn Bảo', role: 'BA', avatar: userAvatar, color: '#FFA500' },
  //   { id: 7, name: 'Lê Tuấn', role: 'UX/UI Design', avatar: userAvatar, color: '#FFA500' },
  //   { id: 8, name: 'Team điều hành', type: 'team', color: '#A6DCEF' },
  //   { id: 9, name: 'Hữu Trung', role: 'UX/UI Design', avatar: userAvatar, color: '#7FB3D5' },
  //   { id: 10, name: 'Trí Thành', role: 'BA Leader', avatar: userAvatar, color: '#D2B48C' },
  //   { id: 11, name: 'Nguyễn Hoàng', role: 'Product Designer', avatar: userAvatar, color: '#D2B48C' },
  //   { id: 12, name: 'Phạm Hương', role: 'Frontend Developer', avatar: userAvatar, color: '#ADD8E6' },
  // ]);

  // State for checked members
  const [checkedMembers, setCheckedMembers] = useState({});

  // Helper: kiểm tra có ai được tích không
  const hasChecked = Object.values(checkedMembers).some(Boolean);

  // Import tasks from storage
  // const [tasks, setTasks] = useState(timelineData);

  // State cho drag/resize
  const [dragInfo, setDragInfo] = useState(null); // { taskId, type, startX, origStart, origEnd, origStartDayIndex, origEndDayIndex, dragOffsetX }
  const [dragOffsetX, setDragOffsetX] = useState(0); // offset pixel khi kéo

  // Ref cho grid để lấy vị trí lề trái
  const gridRef = useRef(null);

  // Preload function
  const preloadTimeline = async (projectId) => {
    if (timelinePreloadPromiseMap[projectId]) return timelinePreloadPromiseMap[projectId];
    timelinePreloadPromiseMap[projectId] = (async () => {
      try {
        const [membersRes, tasksRes] = await Promise.all([
          getProjectMembers(projectId),
          getProjectTasks(projectId)
        ]);
        // Transform members
        const rawMembers = (membersRes.data || membersRes || []).map(m => m.user || m);
        const transformedMembers = rawMembers.map(transformUserData);
        // Transform tasks
        const rawTasks = tasksRes.data || tasksRes || [];
        const transformedTasks = transformTaskListData(rawTasks).map(task => ({
          ...task,
          startDate: task.startDate ? parseDate(task.startDate) : null,
          endDate: task.dueDate ? parseDate(task.dueDate) : null,
          // Lấy assigneeId từ assignee đầu tiên để backward compatibility với Timeline
          assigneeId: task.assignee && task.assignee[0] ? task.assignee[0].userId : null,
          // Lưu tất cả assignees để hiển thị tooltip
          allAssignees: task.assignee || []
        }));
        timelineCache[projectId] = { teamMembers: transformedMembers, tasks: transformedTasks };
        timelineCacheTimestamp[projectId] = Date.now();
        return { teamMembers: transformedMembers, tasks: transformedTasks };
      } catch {
        return { teamMembers: [], tasks: [] };
      }
    })();
    return timelinePreloadPromiseMap[projectId];
  };

  // Fetch members and tasks from API
  useEffect(() => {
    if (!projectId) return;
    const now = Date.now();
    if (timelineCache[projectId] && (now - timelineCacheTimestamp[projectId]) < TIMELINE_CACHE_DURATION) {
      setTeamMembers(timelineCache[projectId].teamMembers);
      setTasks(timelineCache[projectId].tasks);
      setLoading(false);
      return;
    }
    setLoading(true);
    if (timelinePreloadPromiseMap[projectId]) {
      timelinePreloadPromiseMap[projectId].then(({ teamMembers, tasks }) => {
        setTeamMembers(teamMembers);
        setTasks(tasks);
        setLoading(false);
      });
    } else {
      Promise.all([
        getProjectMembers(projectId),
        getProjectTasks(projectId)
      ])
        .then(async ([membersRes, tasksRes]) => {
          // Transform members
          const rawMembers = (membersRes.data || membersRes || []).map(m => m.user || m);
          const transformedMembers = rawMembers.map(transformUserData);
          setTeamMembers(transformedMembers);
          // Transform tasks
          const rawTasks = tasksRes.data || tasksRes || [];
          const transformedTasks = transformTaskListData(rawTasks).map(task => ({
            ...task,
            startDate: task.startDate ? parseDate(task.startDate) : null,
            endDate: task.dueDate ? parseDate(task.dueDate) : null,
            // Lấy assigneeId từ assignee đầu tiên để backward compatibility với Timeline
            assigneeId: task.assignee && task.assignee[0] ? task.assignee[0].userId : null,
            // Lưu tất cả assignees để hiển thị tooltip
            allAssignees: task.assignee || []
          }));
          setTasks(transformedTasks);
          timelineCache[projectId] = { teamMembers: transformedMembers, tasks: transformedTasks };
          timelineCacheTimestamp[projectId] = Date.now();
          setLoading(false);
        })
        .catch((err) => {
          setError(err.message || 'Lỗi khi tải dữ liệu');
          setLoading(false);
        });
    }
  }, [projectId]);

  useEffect(() => { if (projectId) preloadTimeline(projectId); }, [projectId]);

  // Helper to parse DD/MM/YYYY to Date
  const parseDate = (dateStr) => {
    if (!dateStr) return null;
    const [day, month, year] = dateStr.split('/').map(Number);
    return new Date(year, month - 1, day);
  };

  // Handle checkbox change
  const handleCheckboxChange = (memberId) => {
    setCheckedMembers(prev => {
      const newState = { ...prev };
      newState[memberId] = !prev[memberId];
      return newState;
    });
  };

  // Handle team checkbox change
  const handleTeamCheckboxChange = (teamId) => {
    // Get all members of this team
    const teamMemberIds = teamMembers
      .filter(m => {
        // Find the index of the team in the array
        const teamIndex = teamMembers.findIndex(t => t.id === teamId);
        if (teamIndex === -1) return false;
        
        // Find the index of the next team in the array
        const nextTeamIndex = teamMembers.findIndex((t, i) => 
          i > teamIndex && t.type === 'team'
        );
        
        // If there's no next team, include all members after the current team
        if (nextTeamIndex === -1) {
          return teamMembers.indexOf(m) > teamIndex;
        }
        
        // Include all members between current team and next team
        return teamMembers.indexOf(m) > teamIndex && 
               teamMembers.indexOf(m) < nextTeamIndex;
      })
      .map(m => m.id);
    
    // Check if all team members are currently checked
    const allChecked = teamMemberIds.every(id => checkedMembers[id]);
    
    // Toggle all team members
    setCheckedMembers(prev => {
      const newState = { ...prev };
      teamMemberIds.forEach(id => {
        newState[id] = !allChecked;
      });
      // Also toggle the team itself
      newState[teamId] = !allChecked;
      return newState;
    });
  };

  // Get days in month function
  const getDaysInMonth = (year, month) => {
    return new Date(year, month + 1, 0).getDate();
  };

  // Get current month details
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth(); // 0-indexed (0 = January)
  const daysInMonth = getDaysInMonth(currentYear, currentMonth);
  
  // Generate weekday headers (Th2, Th3, etc.)
  const weekdays = ['CN', 'Th2', 'Th3', 'Th4', 'Th5', 'Th6', 'Th7'];

  // Tạo dữ liệu cho các tuần trong tháng
  const generateWeeksInMonth = () => {
    // Lấy ngày đầu tiên của tháng
    const firstDayOfMonth = new Date(currentYear, currentMonth, 1);
    
    // Lấy thứ của ngày đầu tiên (0 = CN, 1 = Th2, ...)
    let firstDayWeekday = firstDayOfMonth.getDay();
    
    // Điều chỉnh để thứ 2 là ngày đầu tuần
    if (firstDayWeekday === 0) firstDayWeekday = 7; // Chủ nhật sẽ là ngày cuối tuần (7)
    
    // Tính ngày bắt đầu của tuần đầu tiên (có thể là ngày của tháng trước)
    const firstDayOfCalendar = new Date(firstDayOfMonth);
    firstDayOfCalendar.setDate(1 - (firstDayWeekday === 0 ? 7 : firstDayWeekday) + 1);
    
    // Tạo mảng các tuần
    const weeks = [];
    let currentDate = new Date(firstDayOfCalendar);
    
    // Tạo đủ 6 tuần để hiển thị đầy đủ tháng
    for (let weekIndex = 0; weekIndex < 6; weekIndex++) {
      const week = [];
      
      // Tạo 7 ngày cho mỗi tuần
      for (let dayIndex = 0; dayIndex < 7; dayIndex++) {
        // Thứ 2 đến thứ 7, sau đó là chủ nhật
        const adjustedDayIndex = dayIndex === 6 ? 0 : dayIndex + 1;
        
        const dayDate = new Date(currentDate);
        dayDate.setDate(currentDate.getDate() + dayIndex);
        
        week.push({
          date: new Date(dayDate),
          day: dayDate.getDate(),
          month: dayDate.getMonth(),
          year: dayDate.getFullYear(),
          weekday: weekdays[adjustedDayIndex],
          isCurrentMonth: dayDate.getMonth() === currentMonth,
          isSunday: adjustedDayIndex === 0,
          isToday: dayDate.toDateString() === new Date().toDateString(),
          isSelected: dayDate.toDateString() === currentDate.toDateString()
        });
      }
      
      weeks.push(week);
      // Di chuyển đến tuần tiếp theo
      currentDate.setDate(currentDate.getDate() + 7);
    }
    
    return weeks;
  };

  const weeksInMonth = generateWeeksInMonth();
  
  // Làm phẳng mảng các ngày trong tháng
  const daysInMonthArray = weeksInMonth.flat().filter(day => day.isCurrentMonth);

  // Function to check if the day is today
  const isToday = (date) => {
    const todayDate = new Date();
    return date.getDate() === todayDate.getDate() && 
           date.getMonth() === todayDate.getMonth() && 
           date.getFullYear() === todayDate.getFullYear();
  };

  // Function to get tasks for a specific team member
  const getTasksForMember = (memberId) => {
    return tasks.filter(task => task.assigneeId === memberId);
  };

  // Function để tính toán vị trí ngày dựa trên pixel
  const getDayIndexFromX = (x) => {
    const dayWidth = 60;
    return Math.round(x / dayWidth);
  };

  // Mouse event handlers cho drag/resize
  useEffect(() => {
    if (!dragInfo) return;
    const handleMouseMove = (e) => {
      const mouseX = e.type.startsWith('touch') ? e.touches[0].clientX : e.clientX;
      const deltaX = mouseX - dragInfo.startX;
      setDragOffsetX(deltaX);
      console.log('[Timeline] handleMouseMove', { deltaX, dragInfo });
    };
    const handleMouseUp = async () => {
      console.log('[Timeline] handleMouseUp', dragInfo);
      let updatedTask = null;
      setTasks(prevTasks => prevTasks.map(task => {
        if (task.id !== dragInfo.taskId) return task;
        let newStart = new Date(dragInfo.origStart);
        let newEnd = new Date(dragInfo.origEnd);
        const dayWidth = 60;
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        if (dragInfo.type === 'move') {
          const dayDelta = Math.round(dragOffsetX / dayWidth);
          newStart = new Date(dragInfo.origStart);
          newStart.setDate(newStart.getDate() + dayDelta);
          newEnd = new Date(dragInfo.origEnd);
          newEnd.setDate(newEnd.getDate() + dayDelta);
          if (newStart < today) {
            return task;
          }
        } else if (dragInfo.type === 'resize-left') {
          const dayDelta = Math.round(dragOffsetX / dayWidth);
          const minIndex = Math.min(dragInfo.origStartDayIndex + dayDelta, dragInfo.origEndDayIndex - 1);
          if (minIndex >= 0 && minIndex < daysInMonthArray.length) {
            const newDate = daysInMonthArray[minIndex].date;
            if (newDate <= newEnd && new Date(newDate) >= today) {
              newStart = new Date(newDate);
            } else {
              return task;
            }
          }
        } else if (dragInfo.type === 'resize-right') {
          const dayDelta = Math.round(dragOffsetX / dayWidth);
          const maxIndex = Math.max(dragInfo.origEndDayIndex + dayDelta, dragInfo.origStartDayIndex + 1);
          if (maxIndex >= 0 && maxIndex < daysInMonthArray.length) {
            const newDate = daysInMonthArray[maxIndex].date;
            if (newDate >= newStart) newEnd = new Date(newDate);
          }
        }
        updatedTask = { ...task, startDate: newStart, endDate: newEnd };
        return updatedTask;
      }));
      setDragInfo(null);
      setDragOffsetX(0);
      if (updatedTask) {
        const oldStart = dragInfo.origStart;
        const oldEnd = dragInfo.origEnd;
        const updatedStartTime = updatedTask.startDate.getTime();
        const oldStartTime = oldStart.getTime();
        const updatedEndTime = updatedTask.endDate.getTime();
        const oldEndTime = oldEnd.getTime();
        console.log('[Timeline] So sánh ngày:', {
          updatedStart: updatedTask.startDate,
          oldStart,
          updatedEnd: updatedTask.endDate,
          oldEnd,
          updatedStartTime,
          oldStartTime,
          updatedEndTime,
          oldEndTime,
          diffStart: updatedStartTime !== oldStartTime,
          diffEnd: updatedEndTime !== oldEndTime,
        });
        if (
          updatedStartTime !== oldStartTime ||
          updatedEndTime !== oldEndTime
        ) {
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          if (updatedTask.startDate < today) {
            alert('Không thể đặt thời gian bắt đầu vào quá khứ');
            setTasks(prevTasks => prevTasks.map(task =>
              task.id === updatedTask.id ?
                { ...task, startDate: oldStart, endDate: oldEnd } :
                task
            ));
            return;
          }
          try {
            // Chuẩn bị payload giống JobUpdate.jsx
            const payload = {
              title: updatedTask.name || updatedTask.title,
              startDate: updatedTask.startDate ? updatedTask.startDate.toISOString() : null,
              dueDate: updatedTask.endDate ? updatedTask.endDate.toISOString() : null,
            };
            if (updatedTask.priority) payload.priority = updatedTask.priority;
            if (updatedTask.status) payload.status = updatedTask.status;
            if (updatedTask.assigneeId) payload.assignedToIds = [updatedTask.assigneeId];
            if (updatedTask.description) payload.description = updatedTask.description;
            console.log('[Timeline] updateProjectTask called', { projectId, id: updatedTask.id, payload });
            const response = await updateProjectTask(
              projectId,
              updatedTask.id,
              payload
            );
            console.log('[Timeline] updateProjectTask response', response);
            // Refetch lại tasks từ API để đảm bảo dữ liệu mới nhất
            const tasksRes = await getProjectTasks(projectId);
            const rawTasks = tasksRes.data || tasksRes || [];
            const transformedTasks = transformTaskListData(rawTasks).map(task => ({
              ...task,
              startDate: task.startDate ? parseDate(task.startDate) : null,
              endDate: task.dueDate ? parseDate(task.dueDate) : null,
              assigneeId: task.assignee && task.assignee[0] ? task.assignee[0].userId : null,
              allAssignees: task.assignee || []
            }));
            setTasks(transformedTasks);
          } catch (err) {
            console.error('[Timeline] updateProjectTask error', err);
            alert('Cập nhật thời gian công việc thất bại: ' + (err.message || 'Unknown error'));
            setTasks(prevTasks => prevTasks.map(task =>
              task.id === updatedTask.id ?
                { ...task, startDate: oldStart, endDate: oldEnd } :
                task
            ));
          }
        }
      }
    };
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);
    window.addEventListener('touchmove', handleMouseMove);
    window.addEventListener('touchend', handleMouseUp);
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
      window.removeEventListener('touchmove', handleMouseMove);
      window.removeEventListener('touchend', handleMouseUp);
    };
  }, [dragInfo, daysInMonthArray, dragOffsetX, projectId]);

  // Function to format date as DD/MM/YYYY
  const formatDateForDetail = (date) => {
    if (!date) return '';
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  // Function to handle task click
  const handleTaskClick = (task) => {
    // Find the member associated with this task
    const member = teamMembers.find(m => m.id === task.assigneeId);
    
    // Create a copy of the task with properly formatted dates and any missing properties
    const taskWithFormattedDates = {
      ...task,
      startDate: formatDateForDetail(task.startDate),
      // Make sure we have all required properties for JobDetail
      assignee: task.assignee || (member ? [{ name: member.name, avatar: member.avatar || userAvatar }] : []),
      creator: task.creator || { name: 'Project Manager', avatar: userAvatar },
      activities: task.activities || [],
      attachments: task.attachments || [],
      description: task.description || `Task for ${member ? member.name : 'team member'}`,
      code: task.code || `TASK-${task.id}`,
      progress: task.progress || 0
    };
    setSelectedTask(taskWithFormattedDates);
  };

  // Function to render tasks for a team member (dùng cho grid)
  const renderTasksGrid = (member) => {
    // Luôn render nếu không ai được tích, hoặc nếu member được tích
    if (hasChecked && !checkedMembers[member.id]) return null;
    if (member.type === 'team') return null;
    const memberTasks = tasks.filter(task => task.assigneeId === member.id);

    // Sắp xếp tasks theo ngày bắt đầu
    const sortedTasks = memberTasks.sort((a, b) => {
      if (!a.startDate || !b.startDate) return 0;
      return a.startDate.getTime() - b.startDate.getTime();
    });

    // Tính toán tầng cho mỗi task để tránh đè lên nhau
    const taskLevels = [];
    sortedTasks.forEach((task, index) => {
      if (!task.startDate || !task.endDate) return;

      let level = 0;
      // Kiểm tra xem task này có bị trùng với task nào ở tầng thấp hơn không
      for (let i = 0; i < index; i++) {
        const prevTask = sortedTasks[i];
        if (!prevTask.startDate || !prevTask.endDate) continue;

        // Kiểm tra overlap: task hiện tại bắt đầu trước khi task trước kết thúc
        const currentStart = task.startDate.getTime();
        const currentEnd = task.endDate.getTime();
        const prevStart = prevTask.startDate.getTime();
        const prevEnd = prevTask.endDate.getTime();

        // Nếu có overlap, tăng level
        if (currentStart <= prevEnd && currentEnd >= prevStart) {
          const prevLevel = taskLevels.find(tl => tl.taskId === prevTask.id)?.level || 0;
          level = Math.max(level, prevLevel + 1);
        }
      }

      taskLevels.push({ taskId: task.id, level });
    });
    return sortedTasks.map((task) => {
      if (!task.startDate || !task.endDate) return null; // Fix lỗi getDate of null
      // Tính số ngày của task
      const startDayIndex = daysInMonthArray.findIndex(day => 
        day.date.getDate() === task.startDate.getDate() && 
        day.date.getMonth() === task.startDate.getMonth() &&
        day.date.getFullYear() === task.startDate.getFullYear()
      );
      const endDayIndex = daysInMonthArray.findIndex(day => 
        day.date.getDate() === task.endDate.getDate() && 
        day.date.getMonth() === task.endDate.getMonth() &&
        day.date.getFullYear() === task.endDate.getFullYear()
      );
      if (startDayIndex === -1) return null;

      // Tìm level của task này
      const taskLevel = taskLevels.find(tl => tl.taskId === task.id)?.level || 0;
      const isDragging = dragInfo && dragInfo.taskId === task.id;
      const onDragStart = (type) => (e) => {
        e.stopPropagation();
        if (e.type === 'mousedown') {
          e.preventDefault();
        }
        const clientX = e.type.startsWith('touch') ? e.touches[0].clientX : e.clientX;
        console.log('[Timeline] onDragStart', { type, clientX, task });
        setDragInfo({
          taskId: task.id,
          type,
          startX: clientX,
          origStart: task.startDate,
          origEnd: task.endDate,
          origStartDayIndex: startDayIndex,
          origEndDayIndex: endDayIndex
        });
        setDragOffsetX(0);
      };
      // Tính số ngày kéo dài
      const dayCount = (endDayIndex !== -1 ? endDayIndex : startDayIndex) - startDayIndex + 1;
      // Avatar nhỏ nếu task chỉ 1 ngày, bình thường nếu dài hơn
      const avatarSize = dayCount <= 1 ? 16 : dayCount === 2 ? 20 : 24;

      // Màu theo trạng thái
      const statusBgColorMap = {
        completed: '#e6ffed',    // xanh lá nhạt
        overdue: '#ffeaea',      // đỏ nhạt
        review: '#f3e8ff',      // tím nhạt
        in_progress: '#e6f7ff', // xanh dương nhạt
        pending: '#fffbe6',     // vàng nhạt
        waiting: '#f5f5f5',     // xám nhạt
      };
      const bgColor = statusBgColorMap[task.status] || '#f0f0f0';
      const textColor = '#222';

      return (
        <div
          key={task.id}
          className={`timeline-task task-${task.type}`}
          style={{
            position: 'absolute',
            left: `${startDayIndex * 69}px`,
            width: `${(endDayIndex !== -1 ? endDayIndex - startDayIndex + 1 : 1) * 69 - 4}px`,
            top: `${7 + taskLevel * 40}px`,
            cursor: 'move',
            userSelect: isDragging ? 'none' : 'auto',
            zIndex: isDragging ? 1000 : 2 + taskLevel,
            transition: isDragging ? 'none' : 'transform 0.2s',
            transform: isDragging && dragInfo.type === 'move' ? `translateX(${dragOffsetX}px)` : 'none',
            display: 'flex',
            alignItems: 'center',
            height: '36px',
            borderRadius: '4px',
            padding: '0 8px',
            fontSize: '13px',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
            backgroundColor: bgColor,
            color: textColor,
          }}
          onMouseDown={onDragStart('move')}
          onTouchStart={onDragStart('move')}
          onClick={(e) => {
            if (!dragInfo) {
              e.stopPropagation();
              handleTaskClick(task);
            }
          }}
          title={`${task.name} - ${task.allAssignees && task.allAssignees.length > 0 
            ? task.allAssignees.map(a => a.name).join(', ') 
            : 'Chưa giao việc'}`}
        >
          <div
            className="resize-handle left"
            style={{ position: 'absolute', left: 0, top: 0, width: 8, height: '100%', cursor: 'ew-resize', zIndex: 2 }}
            onMouseDown={onDragStart('resize-left')}
            onTouchStart={onDragStart('resize-left')}
          />
          <span style={{
            flex: 1,
            minWidth: 0,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            marginRight: 8
          }}>{task.name}</span>
          <img
            src={member.avatar}
            alt={member.name}
            style={{
              width: avatarSize,
              height: avatarSize,
              borderRadius: '50%',
              objectFit: 'cover',
              flexShrink: 0,
              border: '2px solid #fff',
              boxShadow: '0 1px 3px rgba(0,0,0,0.08)',
              marginLeft: 'auto',
            }}
          />
          <div
            className="resize-handle right"
            style={{ position: 'absolute', right: 0, top: 0, width: 8, height: '100%', cursor: 'ew-resize', zIndex: 2 }}
            onMouseDown={onDragStart('resize-right')}
            onTouchStart={onDragStart('resize-right')}
          />
        </div>
      );
    }).filter(Boolean);
  };

  // All month names
  const monthNames = [
    'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 
    'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
  ];

  // Generate years range (from 2020 to current year + 5)
  const endYear = new Date().getFullYear() + 5;
  const years = Array.from({ length: endYear - 2020 + 1 }, (_, i) => 2020 + i);

  // Function to format date as Month/Year
  const formatMonthYear = (date) => {
    return `${monthNames[date.getMonth()]}/${date.getFullYear()}`;
  };

  // Date picker handler
  const toggleDatePicker = () => {
    setIsDatePickerOpen(!isDatePickerOpen);
    setIsYearSelectOpen(false);
    setIsMonthSelectOpen(false);
  };

  // Toggle year select
  const toggleYearSelect = (e) => {
    e.stopPropagation();
    setIsYearSelectOpen(!isYearSelectOpen);
    setIsMonthSelectOpen(false);
  };

  // Toggle month select
  const toggleMonthSelect = (e) => {
    e.stopPropagation();
    setIsMonthSelectOpen(!isMonthSelectOpen);
    setIsYearSelectOpen(false);
  };

  // Function to handle month navigation
  const navigateMonth = (direction) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() + direction);
    setCurrentDate(newDate);
  };

  // Function to handle year selection
  const selectYear = (year) => {
    const newDate = new Date(currentDate);
    newDate.setFullYear(year);
    setCurrentDate(newDate);
    setIsYearSelectOpen(false);
  };

  // Function to handle month selection
  const selectMonth = (monthIndex) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(monthIndex);
    setCurrentDate(newDate);
    setIsMonthSelectOpen(false);
  };

  // Function to go to today
  const goToToday = () => {
    setCurrentDate(new Date());
    setIsDatePickerOpen(false);
  };

  // Function to select a specific date
  const selectDate = (day) => {
    const newDate = new Date(currentYear, currentMonth, day);
    setCurrentDate(newDate);
    setIsDatePickerOpen(false);
  };

  // Close date picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (datePickerRef.current && !datePickerRef.current.contains(event.target)) {
        setIsDatePickerOpen(false);
        setIsYearSelectOpen(false);
        setIsMonthSelectOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Close year select when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (yearSelectRef.current && !yearSelectRef.current.contains(event.target) && 
          !event.target.classList.contains('year-selector')) {
        setIsYearSelectOpen(false);
      }
    };

    if (isYearSelectOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isYearSelectOpen]);

  // Close month select when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (monthSelectRef.current && !monthSelectRef.current.contains(event.target) && 
          !event.target.classList.contains('month-selector')) {
        setIsMonthSelectOpen(false);
      }
    };

    if (isMonthSelectOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMonthSelectOpen]);

  // Generate days for the date picker
  const generateCalendarDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    // Get the first day of the month
    const firstDay = new Date(year, month, 1).getDay();
    
    // Get days in current month
    const daysInCurrentMonth = getDaysInMonth(year, month);
    
    // Get days in previous month
    const daysInPrevMonth = getDaysInMonth(year, month - 1);
    
    const calendarDays = [];
    
    // Add previous month days
    for (let i = firstDay - 1; i >= 0; i--) {
      calendarDays.push({
        day: daysInPrevMonth - i,
        currentMonth: false,
        isToday: false
      });
    }
    
    // Add current month days
    const todayDate = new Date();
    for (let i = 1; i <= daysInCurrentMonth; i++) {
      calendarDays.push({
        day: i,
        currentMonth: true,
        isToday: i === todayDate.getDate() && 
                month === todayDate.getMonth() && 
                year === todayDate.getFullYear()
      });
    }
    
    // Add next month days to fill calendar
    const remainingDays = 42 - calendarDays.length; // 6 rows x 7 days
    for (let i = 1; i <= remainingDays; i++) {
      calendarDays.push({
        day: i,
        currentMonth: false,
        isToday: false
      });
    }
    
    return calendarDays;
  };

  // Render loading and error states
  if (loading) {
    return (
      <div className="timeline-container">
        <div className="timeline-header"></div>
        <div className="month-selector">
          <button className="month-button" disabled style={{ opacity: 0.7 }}>
            <span className="icon">
              <img src={todayIcon} alt="Calendar" />
            </span>
            <div style={{ width: 100, height: 18, background: '#f0f0f0', borderRadius: 4, display: 'inline-block', animation: 'pulse 1.5s ease-in-out infinite' }}></div>
          </button>
        </div>
        <div className="timeline-content">
          <div className="timeline-sidebar">
            <div className="sidebar-header">
              Thành viên dự án <span className="member-count">...</span>
            </div>
            <div className="team-members-list">
              {Array.from({ length: 6 }).map((_, idx) => (
                <div key={idx} className="team-member">
                  <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                    <div style={{ width: 32, height: 32, borderRadius: '50%', background: '#f0f0f0', animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                    <div style={{ width: 80, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="timeline-grid-container">
            <div className="timeline-days-header">
              {Array.from({ length: 30 }).map((_, idx) => (
                <div key={idx} className="day-column">
                  <div style={{ width: 24, height: 14, background: '#f0f0f0', borderRadius: 4, margin: '0 auto', animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                  <div style={{ width: 24, height: 10, background: '#f0f0f0', borderRadius: 4, margin: '0 auto', animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                </div>
              ))}
            </div>
            <div className="timeline-grid" style={{ display: 'grid', gridTemplateColumns: `repeat(30, 69px)`, gridAutoRows: '50px', position: 'relative', background: '#fff' }}>
              {Array.from({ length: 6 }).map((_, rowIdx) => (
                <div key={rowIdx} style={{ gridColumn: '1 / span 30', gridRow: rowIdx + 1, height: 40, background: 'none' }}>
                  {/* Skeleton row */}
                </div>
              ))}
            </div>
          </div>
        </div>
        <style>{`
          @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
          }
        `}</style>
      </div>
    );
  }
  if (error) {
    return <div style={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: 350, color: 'red'}}>{error}</div>;
  }

  return (
    <div className="timeline-container">
      <div className="timeline-header">
        {/* Empty header for spacing */}
      </div>
      
      <div className="month-selector">
        <button className="month-button" onClick={toggleDatePicker}>
          <span className="icon">
            <img src={todayIcon} alt="Calendar" />
          </span>
          {formatMonthYear(currentDate)}
        </button>
        
        {isDatePickerOpen && (
          <div className="date-picker-container" ref={datePickerRef}>
            <div className="date-picker-header">
              <button onClick={() => navigateMonth(-1)} className="month-nav-btn">
                &lt;
              </button>
              <div className="date-picker-selectors">
                <button 
                  className="month-selector-btn" 
                  onClick={toggleMonthSelect}
                >
                  {monthNames[currentMonth]}
                  <span className="dropdown-arrow">▼</span>
                </button>
                <button 
                  className="year-selector-btn" 
                  onClick={toggleYearSelect}
                >
                  {currentYear}
                  <span className="dropdown-arrow">▼</span>
                </button>

                {isYearSelectOpen && (
                  <div className="year-select-dropdown" ref={yearSelectRef}>
                    <div className="year-select-list">
                      {years.map(year => (
                        <div 
                          key={year} 
                          className={`year-option ${year === currentYear ? 'selected' : ''}`}
                          onClick={() => selectYear(year)}
                        >
                          {year}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {isMonthSelectOpen && (
                  <div className="month-select-dropdown" ref={monthSelectRef}>
                    <div className="month-select-list">
                      {monthNames.map((month, index) => (
                        <div 
                          key={index} 
                          className={`month-option ${index === currentMonth ? 'selected' : ''}`}
                          onClick={() => selectMonth(index)}
                        >
                          {month}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              <button onClick={() => navigateMonth(1)} className="month-nav-btn">
                &gt;
              </button>
            </div>
            
            <div className="date-picker-weekdays">
              {weekdays.map(day => (
                <div key={day} className="date-picker-weekday">{day}</div>
              ))}
            </div>
            
            <div className="date-picker-days">
              {generateCalendarDays().map((dateObj, index) => (
                <div 
                  key={index}
                  className={`date-picker-day ${!dateObj.currentMonth ? 'other-month' : ''} 
                              ${dateObj.isToday ? 'today' : ''} 
                              ${dateObj.day === currentDate.getDate() && dateObj.currentMonth ? 'selected' : ''}`}
                  onClick={() => dateObj.currentMonth && selectDate(dateObj.day)}
                >
                  {dateObj.day}
                </div>
              ))}
            </div>
            
            <div className="date-picker-actions">
              <button onClick={goToToday} className="today-btn">
                Hôm nay
              </button>
            </div>
          </div>
        )}
      </div>

      <div className="timeline-content">
        <div className="timeline-sidebar">
          <div className="sidebar-header">
            Thành viên dự án <span className="member-count">{teamMembers.filter(m => m.type !== 'team').length}</span>
          </div>
          <div className="team-members-list">
            {teamMembers.map(member => (
              <div key={member.id} className={`team-member ${member.type === 'team' ? 'team-header' : ''}`}>
                <label className="member-checkbox-label">
                  <input 
                    type="checkbox" 
                    className="member-checkbox"
                    checked={!!checkedMembers[member.id]} 
                    onChange={() => member.type === 'team' 
                      ? handleTeamCheckboxChange(member.id) 
                      : handleCheckboxChange(member.id)
                    }
                  />
                
                  {member.type === 'team' ? (
                    <div className="team-name">{member.name}</div>
                  ) : (
                    <div className="member-info-container">
                      <div className="member-avatar" style={{ backgroundColor: member.color }}>
                        {member.avatar ? (
                          <img src={member.avatar} alt={member.name} />
                        ) : (
                          member.name.charAt(0)
                        )}
                      </div>
                      <div className="member-info" style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                        <div className="member-name">{member.name}</div>
                        <div className="member-role">{member.role}</div>
                      </div>
                    </div>
                  )}
                </label>
              </div>
            ))}
          </div>
        </div>

        <div className="timeline-grid-container" ref={gridRef}>
          <div className="timeline-days-header">
            {daysInMonthArray.map((dayInfo, index) => (
              <div
                key={index}
                className={`day-column ${dayInfo.isToday ? 'today' : ''} ${dayInfo.isSunday ? 'sunday' : ''}`}
              >
                {dayInfo.isToday && <div className="current-day-indicator"></div>}
                <div className="day-number">{dayInfo.day}</div>
                <div className="weekday">{dayInfo.weekday}</div>
              </div>
            ))}
          </div>
          <div
            className="timeline-grid"
            style={{
              display: 'grid',
              gridTemplateColumns: `repeat(${daysInMonthArray.length}, 69px)`,
              gridAutoRows: 'auto',
              position: 'relative',
              background: '#fff',
            }}
          >
            {/* Sắp xếp: nếu có checked thì đưa các member được tích lên đầu */}
            {(() => {
              let members = teamMembers.filter(m => m.type !== 'team');
              if (hasChecked) {
                const checked = members.filter(m => checkedMembers[m.id]);
                const unchecked = members.filter(m => !checkedMembers[m.id]);
                members = [...checked, ...unchecked];
              }

              // Tính toán số tầng tối đa cho mỗi member để điều chỉnh chiều cao row
              const maxLevelsPerMember = members.map(member => {
                const memberTasks = tasks.filter(task => task.assigneeId === member.id);
                const sortedTasks = memberTasks.sort((a, b) => {
                  if (!a.startDate || !b.startDate) return 0;
                  return a.startDate.getTime() - b.startDate.getTime();
                });

                let maxLevel = 0;
                sortedTasks.forEach((task, index) => {
                  if (!task.startDate || !task.endDate) return;

                  let level = 0;
                  for (let i = 0; i < index; i++) {
                    const prevTask = sortedTasks[i];
                    if (!prevTask.startDate || !prevTask.endDate) continue;

                    const currentStart = task.startDate.getTime();
                    const currentEnd = task.endDate.getTime();
                    const prevStart = prevTask.startDate.getTime();
                    const prevEnd = prevTask.endDate.getTime();

                    if (currentStart <= prevEnd && currentEnd >= prevStart) {
                      level++;
                    }
                  }
                  maxLevel = Math.max(maxLevel, level);
                });

                return maxLevel;
              });

              const minRows = 8;
              const rowCount = members.length;
              const emptyRows = minRows - rowCount > 0 ? minRows - rowCount : 0;
              return <>
                {members.map((member, rowIdx) => {
                  const maxLevel = maxLevelsPerMember[rowIdx] || 0;
                  const rowHeight = Math.max(50, 50 + maxLevel * 40); // Tăng chiều cao theo số tầng

                  return (
                    <div key={`member-row-${member.id}`} style={{
                      gridColumn: `1 / span ${daysInMonthArray.length}`,
                      gridRow: rowIdx + 1,
                      height: rowHeight,
                      background: 'none',
                      position: 'relative'
                    }}>
                      {renderTasksGrid(member)}
                    </div>
                  );
                })}
                {Array.from({ length: emptyRows }).map((_, idx) => (
                  <div
                    key={`empty-row-${idx}`}
                    style={{
                      gridColumn: `1 / span ${daysInMonthArray.length}`,
                      gridRow: rowCount + idx + 1,
                      height: 50,
                      background: 'none'
                    }}
                  />
                ))}
              </>;
            })()}
          </div>
        </div>
      </div>
      
      {/* Add the JobDetail component */}
      {selectedTask && (
        <div style={{ 
          position: 'fixed',
          top: 0,
          right: 0,
          width: '400px',
          height: '100%',
          zIndex: 1000
        }}>
          <DetailJob 
            task={selectedTask} 
            onClose={() => setSelectedTask(null)} 
            hideExport={true}
          />
        </div>
      )}
    </div>
  );
};

export default Timeline;