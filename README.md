# React + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend using TypeScript with type-aware lint rules enabled. Check out the [TS template](https://github.com/vitejs/vite/tree/main/packages/create-vite/template-react-ts) for information on how to integrate TypeScript and [`typescript-eslint`](https://typescript-eslint.io) in your project.

# Project Management System

## Hệ thống cập nhật tự động (Auto Update System)

### Tổng quan
Hệ thống này cho phép các component tự động cập nhật dữ liệu mà không cần reload trang. Khi có thay đổi trong task (tạo, cập nhật, xóa), các component liên quan sẽ tự động refresh dữ liệu.

### Cách hoạt động

#### 1. Event System
Hệ thống sử dụng các sự kiện (events) để thông báo thay đổi:
- `taskCreated`: Khi tạo task mới
- `taskUpdated`: Khi cập nhật task
- `taskStatusUpdated`: Khi thay đổi trạng thái task
- `taskDeleted`: Khi xóa task
- `projectTasksUpdated`: Khi có thay đổi trong project tasks
- `personalTasksUpdated`: Khi có thay đổi trong personal tasks

#### 2. Utility Functions
```javascript
import { triggerTaskUpdate, triggerTaskEvents, TASK_EVENTS } from '../utils/toastUtils';

// Trigger một sự kiện
triggerTaskUpdate(TASK_EVENTS.STATUS_UPDATED);

// Trigger nhiều sự kiện
triggerTaskEvents([TASK_EVENTS.PROJECT_TASKS_UPDATED, TASK_EVENTS.CREATED]);
```

#### 3. Custom Hook
```javascript
import { useTaskUpdateListener } from '../utils/toastUtils';

const MyComponent = () => {
  const handleUpdate = useCallback(() => {
    // Logic refresh data
  }, []);

  useTaskUpdateListener(handleUpdate, [TASK_EVENTS.PROJECT_TASKS_UPDATED]);
};
```

### Cách sử dụng

#### Trong component tạo/cập nhật task:
```javascript
// Sau khi tạo task thành công
triggerTaskEvents([TASK_EVENTS.PROJECT_TASKS_UPDATED, TASK_EVENTS.CREATED]);
```

#### Trong component hiển thị danh sách task:
```javascript
const handleTasksUpdated = useCallback(() => {
  // Refresh data từ API
  loadTasks();
}, [projectId]);

useTaskUpdateListener(handleTasksUpdated, [
  TASK_EVENTS.PROJECT_TASKS_UPDATED,
  TASK_EVENTS.STATUS_UPDATED,
  TASK_EVENTS.DELETED,
  TASK_EVENTS.CREATED
]);
```

### Lợi ích
- Không cần reload trang
- Cập nhật real-time
- Tối ưu performance
- Dễ maintain và mở rộng

### Lưu ý
- Đảm bảo cleanup event listeners khi component unmount
- Sử dụng useCallback để tránh re-render không cần thiết
- Chỉ trigger events khi thực sự cần thiết
