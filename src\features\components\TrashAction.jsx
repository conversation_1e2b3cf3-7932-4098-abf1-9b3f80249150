import React, { useEffect, useRef } from "react";
import RefreshIcon from "../../assets/refresh-ccw.svg";
import TrashIcon from "../../assets/trash.svg";
import "../../styles/ActionTrash.css";

const ActionTrash = ({ onRestore, onDelete, onClose, canRestore = true }) => {
  const ref = useRef();
  useEffect(() => {
    const handleClick = (e) => {
      if (ref.current && !ref.current.contains(e.target)) {
        onClose && onClose();
      }
    };
    document.addEventListener("mousedown", handleClick);
    return () => document.removeEventListener("mousedown", handleClick);
  }, [onClose]);
  return (
    <div className="action-trash" ref={ref}>
      <div className="action-trash-title">Hành động</div>
      <button 
        className="action-trash-btn" 
        onClick={onRestore}
        disabled={!canRestore}
        style={{ 
          opacity: canRestore ? 1 : 0.5,
          cursor: canRestore ? 'pointer' : 'not-allowed'
        }}
      >
        <img src={RefreshIcon} alt="Khôi phục" className="action-trash-icon" />
        Khôi phục
      </button>
      <button className="action-trash-btn delete" onClick={onDelete}>
        <img src={TrashIcon} alt="Xóa vĩnh viễn" className="action-trash-icon" />
        Xóa vĩnh viễn
      </button>
    </div>
  );
};

export default ActionTrash;
