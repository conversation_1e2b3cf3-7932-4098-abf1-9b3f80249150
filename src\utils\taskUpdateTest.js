// Test file để kiểm tra hệ thống cập nhật tự động
import { triggerTaskUpdate, triggerTaskEvents, TASK_EVENTS } from './toastUtils';

// Test function để kiểm tra các events
export const testTaskUpdateSystem = () => {
  console.log('🧪 Testing Task Update System...');
  
  // Test single event
  console.log('📡 Triggering single event: taskCreated');
  triggerTaskUpdate(TASK_EVENTS.CREATED);
  
  // Test multiple events
  console.log('📡 Triggering multiple events: projectTasksUpdated, taskStatusUpdated');
  triggerTaskEvents([TASK_EVENTS.PROJECT_TASKS_UPDATED, TASK_EVENTS.STATUS_UPDATED]);
  
  // Test all events
  console.log('📡 Triggering all events');
  triggerTaskEvents(Object.values(TASK_EVENTS));
  
  console.log('✅ Task Update System test completed');
};

// Function để log events
export const logTaskEvents = () => {
  Object.values(TASK_EVENTS).forEach(eventType => {
    window.addEventListener(eventType, () => {
      console.log(`🎯 Event triggered: ${eventType}`);
    });
  });
};

// Test function để kiểm tra logic filtering tasks
export const testTaskFiltering = () => {
  const currentUserId = "test-user-id";
  
  const mockTasks = [
    {
      _id: "task1",
      title: "Task 1",
      assignedTo: { _id: currentUserId, name: "Test User" },
      createdBy: { _id: "other-user", name: "Other User" },
      watchers: [],
      followerIds: []
    },
    {
      _id: "task2", 
      title: "Task 2",
      assignedTo: null,
      assignedToMultiple: [{ _id: currentUserId, name: "Test User" }],
      createdBy: { _id: "other-user", name: "Other User" },
      watchers: [],
      followerIds: []
    },
    {
      _id: "task3",
      title: "Task 3", 
      assignedTo: null,
      assignedToMultiple: [],
      createdBy: { _id: currentUserId, name: "Test User" },
      watchers: [],
      followerIds: []
    },
    {
      _id: "task4",
      title: "Task 4",
      assignedTo: null,
      assignedToMultiple: [],
      createdBy: { _id: "other-user", name: "Other User" },
      watchers: [{ _id: currentUserId, name: "Test User" }],
      followerIds: []
    },
    {
      _id: "task5",
      title: "Task 5",
      assignedTo: null,
      assignedToMultiple: [],
      createdBy: { _id: "other-user", name: "Other User" },
      watchers: [],
      followerIds: [currentUserId]
    },
    {
      _id: "task6",
      title: "Task 6",
      assignedTo: null,
      assignedToMultiple: [],
      createdBy: { _id: "other-user", name: "Other User" },
      watchers: [],
      followerIds: []
    }
  ];

  const filteredTasks = mockTasks.filter(task => {
    // Kiểm tra nếu user là assignee (assignedTo hoặc assignedToMultiple)
    const isAssignee = 
      // Kiểm tra assignedTo (single user)
      (task.assignedTo && (
        task.assignedTo._id === currentUserId || 
        task.assignedTo.id === currentUserId ||
        task.assignedToId === currentUserId
      )) ||
      // Kiểm tra assignedToMultiple (array of users)
      (task.assignedToMultiple && Array.isArray(task.assignedToMultiple) && 
       task.assignedToMultiple.some(user => 
         user._id === currentUserId || 
         user.id === currentUserId
       )) ||
      // Kiểm tra assignedToIds (array of user IDs)
      (task.assignedToIds && Array.isArray(task.assignedToIds) && 
       task.assignedToIds.includes(currentUserId));

    // Kiểm tra nếu user là watcher/follower
    const isWatcher = 
      (task.watchers && Array.isArray(task.watchers) && 
       task.watchers.some(user => 
         user._id === currentUserId || 
         user.id === currentUserId
       )) ||
      (task.followerIds && Array.isArray(task.followerIds) && 
       task.followerIds.includes(currentUserId));

    // Kiểm tra nếu user là creator
    const isCreator = 
      task.createdBy && (
        task.createdBy._id === currentUserId || 
        task.createdBy.id === currentUserId ||
        task.createdById === currentUserId
      );

    return isAssignee || isWatcher || isCreator;
  });

  console.log("Test Task Filtering Results:");
  console.log("Total tasks:", mockTasks.length);
  console.log("Filtered tasks:", filteredTasks.length);
  console.log("Filtered task IDs:", filteredTasks.map(t => t._id));
  
  return {
    total: mockTasks.length,
    filtered: filteredTasks.length,
    expected: 5, // Should include tasks 1, 2, 3, 4, 5
    success: filteredTasks.length === 5
  };
};

// Test function để kiểm tra việc lấy tất cả tasks
export const testGetAllTasks = async () => {
  try {
    const { getMyProjectTasks } = await import('../api/taskManagement.js');
    
    console.log('Testing getMyProjectTasks...');
    const startTime = Date.now();
    
    const result = await getMyProjectTasks({
      limit: 1000,
      noLimit: true,
      sort: '-createdAt'
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log('getMyProjectTasks test results:', {
      totalTasks: result.data?.length || 0,
      duration: `${duration}ms`,
      success: true
    });
    
    return {
      totalTasks: result.data?.length || 0,
      duration,
      success: true
    };
  } catch (error) {
    console.error('Error testing getMyProjectTasks:', error);
    return {
      totalTasks: 0,
      duration: 0,
      success: false,
      error: error.message
    };
  }
};

// Export để sử dụng trong development
if (process.env.NODE_ENV === 'development') {
  window.testTaskUpdateSystem = testTaskUpdateSystem;
  window.logTaskEvents = logTaskEvents;
}

if (typeof window !== 'undefined') {
  window.testGetAllTasks = testGetAllTasks;
}
