// Cache và preload để tăng tốc độ loading
let trashCache = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 10000; // 10 giây
let preloadPromise = null;

import ActionTrash from "../../components/TrashAction";
import TrashActionBar from "../../components/TrashActionBar";
import React, { useState, useEffect } from "react";
import "../../../styles/TrashList.css";
import "../../../styles/Trash.css";
import FolderIcon from "../../../assets/file-text.svg";
import SquareCheckIcon from "../../../assets/square-check-big.svg";

import { getDeletedUsers, restoreUser, deleteUser } from "../../../api/userManagement";
import { getDeletedProjects, restoreProject, permanentDeleteProject } from "../../../api/projectManagement";
import { getDeletedProjectTasks, restoreProjectTask, permanentDeleteProjectTask } from "../../../api/taskManagement";
import { getAllProjects } from "../../../api/projectManagement";
import { showSuccess, showError } from "../../../utils/toastUtils";

const statusColor = (status) => {                                                                     
  if (status === "Hoàn thành") return { color: '#27ae60' };
  if (status === "Đang chờ") return { color: '#e67e22' };
  return {};
};

const getIconByType = (type) => {
  switch (type) {
    case "Dự án":
      return FolderIcon;
    case "Công việc":
      return SquareCheckIcon;
    case "Nhân sự":
      return SquareCheckIcon;
    default:
      return FolderIcon;
  }
};

const All = ({ onDataChange }) => {
  const [trashData, setTrashData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [actionIdx, setActionIdx] = useState(null);
  const [selected, setSelected] = useState([]);
  const [lastStats, setLastStats] = useState(null); // Để tránh gọi callback liên tục

  // Hàm gửi thống kê lên component cha
  const sendStatsToParent = (allTrash, userTrash, filteredProjectTrash, filteredTaskTrash = []) => {
    if (onDataChange) {
      const userCount = userTrash.length;
      const projectCount = filteredProjectTrash.length;
      const taskCount = filteredTaskTrash.length;
      const stats = {
        totalItems: allTrash.length,
        projectCount: projectCount,
        jobCount: taskCount,
        userCount: userCount,
        selectedCount: selected.length
      };
      
      // Chỉ gửi nếu thống kê thay đổi
      if (JSON.stringify(stats) !== JSON.stringify(lastStats)) {
        setLastStats(stats);
        onDataChange(stats);
      }
    }
  };

  // Preload function
  const preloadTrashData = async () => {
    if (preloadPromise) return preloadPromise;

    preloadPromise = (async () => {
      try {
        // Fetch từng API riêng lẻ để tránh một lỗi làm fail toàn bộ
        let users = [];
        let projects = [];
        let tasks = [];

        try {
          const usersResponse = await getDeletedUsers();
          users = usersResponse?.data || usersResponse || [];
        } catch (err) {
          console.warn('Error fetching deleted users:', err);
          users = [];
        }

        try {
          const projectsResponse = await getDeletedProjects();
          projects = projectsResponse?.data || projectsResponse || [];
        } catch (err) {
          console.warn('Error fetching deleted projects:', err);
          projects = [];
        }

        // Thêm fetch task đã xóa (dự án)
        try {
          // Lấy tất cả projectId và thông tin project
          let allProjectIds = [];
          const projectsMap = {};
          try {
            const projectsRes = await getAllProjects();
            const allProjects = projectsRes.data || projectsRes || [];
            allProjectIds = allProjects.map(p => p._id || p.id);
            // Tạo map thông tin project
            allProjects.forEach(project => {
              projectsMap[project._id || project.id] = {
                name: project.name || 'Không có tên',
                projectCode: project.projectCode || 'N/A'
              };
            });
          } catch (err) {
            allProjectIds = [];
          }
          
          // Lấy task đã xóa cho từng project
          for (const projectId of allProjectIds) {
            try {
              const tasksResponse = await getDeletedProjectTasks(projectId);
              const projectTasks = tasksResponse?.data || tasksResponse || [];
              // Gán projectId và thông tin project vào mỗi task
              const tasksWithProjectId = projectTasks.map(t => {
                // Xác định subtask chính xác hơn - kiểm tra cả parentTaskId và taskCode
                const isSubtask = t.parentTaskId && (
                  (t.parentTaskId._id || t.parentTaskId.id || t.parentTaskId) ||
                  (typeof t.parentTaskId === 'object' && t.parentTaskId !== null && Object.keys(t.parentTaskId).length > 0)
                ) || (t.taskCode && t.taskCode.startsWith('SUB-'));
                
                // Tạo parentTaskInfo cho subtask - cải thiện logic để đảm bảo luôn có thông tin
                let parentTaskInfo = null;
                if (isSubtask) {
                  if (t.parentTaskId && typeof t.parentTaskId === 'object' && t.parentTaskId._id) {
                    // Nếu parentTaskId được populate đầy đủ
                    parentTaskInfo = {
                      title: t.parentTaskId.title || t.parentTaskId.name || 'Không có tiêu đề',
                      taskCode: t.parentTaskId.taskCode || t.parentTaskId.code || 'N/A',
                      id: t.parentTaskId._id || t.parentTaskId.id || t.parentTaskId,
                      isDeleted: t.parentTaskId.isDeleted || false
                    };
                  } else if (t.parentTaskId && typeof t.parentTaskId === 'string') {
                    // Nếu parentTaskId chỉ là ID string, cần tìm task chính trong cùng project
                    // Tìm trong data hiện tại trước
                    const parentTask = projectTasks.find(pt => 
                      (pt._id || pt.id) === t.parentTaskId || 
                      (pt.taskCode && pt.taskCode === t.parentTaskId)
                    );
                    if (parentTask) {
                      parentTaskInfo = {
                        title: parentTask.title || parentTask.name || 'Không có tiêu đề',
                        taskCode: parentTask.taskCode || parentTask.code || 'N/A',
                        id: parentTask._id || parentTask.id,
                        isDeleted: parentTask.isDeleted || false
                      };
                    } else {
                      // Nếu không tìm thấy parent task trong danh sách đã xóa, có thể nó chưa bị xóa
                      // hoặc đã bị xóa vĩnh viễn. Trong trường hợp này, coi như parent task đã bị xóa
                      parentTaskInfo = {
                        title: 'Không thể xác định [Đã bị xóa]',
                        taskCode: 'N/A',
                        id: t.parentTaskId || 'unknown',
                        isDeleted: true // Giả sử đã bị xóa nếu không tìm thấy
                      };
                    }
                  }
                }
                
                // Cập nhật logic canRestore để kiểm tra chính xác hơn
                const parentTaskDeleted = parentTaskInfo ? parentTaskInfo.isDeleted : false;
                const canRestore = !projectsMap[projectId]?.isDeleted && (!isSubtask || !parentTaskDeleted);
                
                return {
                  ...t, 
                  projectId,
                  // Thêm thông tin project nếu có
                  projectInfo: projectsMap[projectId] || { name: 'Không có tên', projectCode: 'N/A', isDeleted: false },
                  // Đảm bảo có các trường cần thiết
                  title: t.title || t.name || t.taskCode || 'Không có tiêu đề',
                  deletedDate: t.deletedAt ? new Date(t.deletedAt).toLocaleDateString('vi-VN') : 'Không xác định',
                  deadline: t.dueDate ? new Date(t.dueDate).toLocaleDateString('vi-VN') : 'Không có deadline',
                  // Thêm thông tin task cha cho subtask
                  parentTaskInfo: parentTaskInfo,
                  // Thêm flag để kiểm tra có thể khôi phục hay không
                  canRestore: canRestore
                };
              });
              tasks = tasks.concat(tasksWithProjectId);
            } catch (err) {
              // Log lỗi ngắn gọn
              console.warn(`Error fetching deleted tasks for project ${projectId}:`, err.message);
            }
          }
        } catch (err) {
          console.warn('Error fetching deleted tasks:', err);
          tasks = [];
        }

        const userTrash = (users || []).map(u => ({ 
          ...u, 
          type: "Nhân sự",
          title: u.fullName || u.name || 'Không có tên',
          deletedDate: u.deletedAt ? new Date(u.deletedAt).toLocaleDateString('vi-VN') : 'Không xác định',
          // Nhân sự có thể khôi phục mặc định
          canRestore: true
        }));
        const projectTrash = (projects || []).map(p => ({ 
          ...p, 
          type: "Dự án",
          title: p.name || p.title || 'Không có tên',
          deletedDate: p.deletedAt ? new Date(p.deletedAt).toLocaleDateString('vi-VN') : 'Không xác định',
          deadline: p.endDate ? new Date(p.endDate).toLocaleDateString('vi-VN') : 'Không có deadline',
          // Dự án là thực thể lớn nhất, luôn có thể khôi phục
          canRestore: true
        }));
        const taskTrash = (tasks || []).map(t => {
          // Sử dụng logic đã được tính toán trước đó
          const isSubtask = t.type === "Công việc phụ" || (t.parentTaskId && (
            (t.parentTaskId._id || t.parentTaskId.id || t.parentTaskId) ||
            (typeof t.parentTaskId === 'object' && t.parentTaskId !== null && Object.keys(t.parentTaskId).length > 0)
          ) || (t.taskCode && t.taskCode.startsWith('SUB-')));
          
          return {
            ...t, 
            type: isSubtask ? "Công việc phụ" : "Công việc",
            title: t.title || t.name || t.taskCode || 'Không có tiêu đề',
            deletedDate: t.deletedAt ? new Date(t.deletedAt).toLocaleDateString('vi-VN') : 'Không xác định',
            deadline: t.dueDate ? new Date(t.dueDate).toLocaleDateString('vi-VN') : 'Không có deadline'
          };
        });

        // Lọc theo quyền
        const userRaw = JSON.parse(localStorage.getItem('user') || '{}');
        const role = userRaw.user?.role?.toLowerCase() || userRaw.role?.toLowerCase() || 'staff';
        const userId = userRaw.user?._id || userRaw._id || userRaw.user?.id || userRaw.id;
        const userDeptId = userRaw.user?.departmentId || userRaw.departmentId || userRaw.user?.department?._id || userRaw.department?._id || userRaw.user?.department || userRaw.department;

        // Lọc cho project
        let filteredProjectTrash = projectTrash;
        // Lọc task theo quyền
        let filteredTaskTrash = taskTrash;

        let allTrash = [...userTrash, ...filteredProjectTrash, ...filteredTaskTrash];

        trashCache = {
          allTrash,
          userTrash,
          filteredProjectTrash,
          filteredTaskTrash
        };
        cacheTimestamp = Date.now();

        return trashCache;
      } catch (err) {
        console.error("Error in preloadTrashData:", err);
        return {
          allTrash: [],
          userTrash: [],
          filteredProjectTrash: [],
          filteredTaskTrash: []
        };
      }
    })();

    return preloadPromise;
  };

  // Fetch tất cả loại thùng rác khi mount
  useEffect(() => {
    const fetchAllTrash = async () => {
      // Kiểm tra cache trước
      const now = Date.now();
      if (trashCache && (now - cacheTimestamp) < CACHE_DURATION) {
        setTrashData(trashCache.allTrash);
        sendStatsToParent(trashCache.allTrash, trashCache.userTrash, trashCache.filteredProjectTrash, trashCache.filteredTaskTrash);
        setLoading(false);
        return;
      }

      // Nếu không có cache, mới set loading=true
      setLoading(true);
      setError(null);

      try {
        let cacheData;
        if (preloadPromise) {
          cacheData = await preloadPromise;
        } else {
          cacheData = await preloadTrashData();
        }

        setTrashData(cacheData.allTrash);
        sendStatsToParent(cacheData.allTrash, cacheData.userTrash, cacheData.filteredProjectTrash, cacheData.filteredTaskTrash);
      } catch (err) {
        console.error("Error in fetchAllTrash:", err);
        setError("Lỗi khi tải dữ liệu thùng rác");
      } finally {
        setLoading(false);
      }
    };
    fetchAllTrash();
  }, []); // Empty dependency array để chỉ chạy một lần

  // Start preloading when component mounts
  useEffect(() => {
    preloadTrashData();
  }, []);

  // Handler API cho từng loại
  const handleRestoreItem = async (item) => {
    try {
      // Dự án: luôn cho phép khôi phục
      if (item.type !== 'Dự án') {
        // Kiểm tra xem item có thể khôi phục không
        if (!item.canRestore) {
          if (item.projectInfo?.isDeleted) {
            showError("Không thể khôi phục vì dự án đã bị xóa!");
            return;
          }
          if (item.type === "Công việc phụ" && item.parentTaskInfo?.isDeleted) {
            showError("Không thể khôi phục công việc phụ vì công việc chính đã bị xóa!");
            return;
          }
        }
      }
      
      if (item.type === "Nhân sự") {
        await restoreUser(item._id || item.id);
      } else if (item.type === "Dự án") {
        await restoreProject(item._id || item.id);
      } else if (item.type === "Công việc" || item.type === "Công việc phụ") {
        if (!item.projectId) throw new Error("Công việc thiếu ID dự án để khôi phục.");
        await restoreProjectTask(item.projectId, item._id || item.id);
      }
      setTrashData(prev => prev.filter(i => (i._id || i.id) !== (item._id || item.id)));
      showSuccess("Khôi phục thành công!");
    } catch (err) {
      showError("Lỗi khi khôi phục: " + (err.message || err));
    }
    setActionIdx(null);
  };
  const handleDeleteItem = async (item) => {
    try {
      if (item.type === "Nhân sự") {
        await deleteUser(item._id || item.id);
      } else if (item.type === "Dự án") {
        await permanentDeleteProject(item._id || item.id);
      } else if (item.type === "Công việc" || item.type === "Công việc phụ") {
        if (!item.projectId) throw new Error("Công việc thiếu ID dự án để xóa.");
        await permanentDeleteProjectTask(item.projectId, item._id || item.id);
      }
      setTrashData(prev => prev.filter(i => (i._id || i.id) !== (item._id || item.id)));
      showSuccess("Xóa vĩnh viễn thành công!");
    } catch (err) {
      showError("Lỗi khi xóa vĩnh viễn: " + (err.message || err));
    }
    setActionIdx(null);
  };
  const handleSelect = (id) => {
    setSelected((prev) =>
      prev.includes(id) ? prev.filter((sid) => sid !== id) : [...prev, id]
    );
  };
  const handleSelectAll = () => {
    if (selected.length === trashData.length) {
      setSelected([]);
    } else {
      setSelected(trashData.map((item) => item._id || item.id));
    }
  };
  const handleClearSelection = () => setSelected([]);

  // Xử lý xóa vĩnh viễn nhiều mục
  const handleDeleteSelected = async () => {
    const itemsToDelete = selected.map(id => trashData.find(i => (i._id || i.id) === id)).filter(Boolean);

    const promises = itemsToDelete.map(item => {
      if (item.type === "Nhân sự") return deleteUser(item._id || item.id);
      if (item.type === "Dự án") return permanentDeleteProject(item._id || item.id);
      if ((item.type === "Công việc" || item.type === "Công việc phụ") && item.projectId) return permanentDeleteProjectTask(item.projectId, item._id || item.id);
      return Promise.reject(new Error(`Không thể xóa mục: ${item.title || item.name}`));
    });

    try {
      await Promise.all(promises);
      setTrashData(prev => prev.filter(item => !selected.includes(item._id || item.id)));
      setSelected([]);
      showSuccess("Xóa vĩnh viễn thành công!");
    } catch (err) {
      showError("Lỗi khi xóa vĩnh viễn hàng loạt: " + (err.message || err));
    }
  };
  // Xử lý khôi phục nhiều mục
  const handleRestoreSelected = async () => {
    const itemsToRestore = selected.map(id => trashData.find(i => (i._id || i.id) === id)).filter(Boolean);

    // Kiểm tra logic khôi phục cho task
    const tasks = itemsToRestore.filter(item => item.type === "Công việc" || item.type === "Công việc phụ");
    const mainTasks = tasks.filter(item => !item.parentTaskId);
    const subTasks = tasks.filter(item => item.parentTaskId);
    
    // Kiểm tra xem có task phụ nào mà task chính không được chọn khôi phục không
    const mainTaskIds = mainTasks.map(task => task._id || task.id);
    const subTasksWithoutParent = subTasks.filter(subTask => 
      !mainTaskIds.includes(subTask.parentTaskId)
    );
    
    if (subTasksWithoutParent.length > 0) {
      const taskNames = subTasksWithoutParent.map(task => task.title).join(', ');
      showError(`Không thể khôi phục các công việc phụ: ${taskNames}. Vui lòng khôi phục công việc chính trước.`);
      return;
    }

    // Thực hiện khôi phục từng item một để xử lý lỗi riêng lẻ
    const results = [];
    const errors = [];

    for (const item of itemsToRestore) {
      try {
        // Kiểm tra xem item có thể khôi phục không
        if (!item.canRestore) {
          if (item.projectInfo?.isDeleted) {
            errors.push({ item, error: "Không thể khôi phục vì dự án đã bị xóa" });
            continue;
          }
          if (item.type === "Công việc phụ" && item.parentTaskInfo?.isDeleted) {
            errors.push({ item, error: "Không thể khôi phục vì công việc chính đã bị xóa" });
            continue;
          }
        }
        
        if (item.type === "Nhân sự") {
          await restoreUser(item._id || item.id);
        } else if (item.type === "Dự án") {
          await restoreProject(item._id || item.id);
        } else if (item.type === "Công việc" || item.type === "Công việc phụ") {
          if (item.projectId) {
            await restoreProjectTask(item.projectId, item._id || item.id);
          } else {
            throw new Error(`Không thể khôi phục mục: ${item.title || item.name}`);
          }
        } else {
          throw new Error(`Không thể khôi phục mục: ${item.title || item.name}`);
        }
        results.push(item);
      } catch (err) {
        errors.push({ item, error: err.message || err });
      }
    }

    // Cập nhật UI
    if (results.length > 0) {
      setTrashData(prev => prev.filter(item => !results.some(r => (r._id || r.id) === (item._id || item.id))));
      setSelected([]);
      showSuccess(`Khôi phục thành công ${results.length} mục!`);
    }

    if (errors.length > 0) {
      const errorMessages = errors.map(e => `${e.item.title || e.item.name}: ${e.error}`).join('\n');
      showError(`Lỗi khi khôi phục ${errors.length} mục:\n${errorMessages}`);
    }
  };

  // Skeleton loading component
  const TrashSkeleton = () => (
    <div className="trash-skeleton-container">
      {[1, 2, 3, 4, 5, 6].map((idx) => (
        <div key={idx} className="trash-skeleton-item">
          <div className="trash-skeleton-checkbox"></div>
          <div className="trash-skeleton-main">
            <div className="trash-skeleton-title-row">
              <div className="trash-skeleton-icon"></div>
              <div className="trash-skeleton-title"></div>
              <div className="trash-skeleton-type"></div>
            </div>
            <div className="trash-skeleton-info">
              <div className="trash-skeleton-text"></div>
              <div className="trash-skeleton-text"></div>
              <div className="trash-skeleton-text"></div>
            </div>
            <div className="trash-skeleton-members">
              <div className="trash-skeleton-member"></div>
              <div className="trash-skeleton-member"></div>
              <div className="trash-skeleton-member"></div>
            </div>
          </div>
          <div className="trash-skeleton-actions">
            <div className="trash-skeleton-action-type"></div>
            <div className="trash-skeleton-more-btn"></div>
          </div>
        </div>
      ))}
    </div>
  );

  if (loading && trashData.length === 0) {
    return (
      <div className="trash-list-container">
        <div className="trash-list-header">
          <div className="trash-list-header-left">
            <input type="checkbox" disabled style={{ opacity: 0.5 }} />
            <span style={{ color: '#666' }}>Đang tải dữ liệu...</span>
          </div>
          <div className="trash-list-header-right" style={{ color: '#666' }}>Đang tải...</div>
        </div>
        <TrashSkeleton />
      </div>
    );
  }

  if (error) return (
    <div style={{
      color: "red",
      textAlign: "center",
      padding: "40px",
      background: "#fff",
      borderRadius: "8px",
      margin: "20px 0"
    }}>
      <div>❌ {error}</div>
      <button
        onClick={() => window.location.reload()}
        style={{
          marginTop: '10px',
          padding: '8px 16px',
          background: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        Thử lại
      </button>
    </div>
  );

  return (
    <div className="trash-list-container">
      {selected.length > 0 && (
        <TrashActionBar
          selectedCount={selected.length}
          onRestore={handleRestoreSelected}
          onDelete={handleDeleteSelected}
          onClearSelection={handleClearSelection}
        />
      )}
      <div className="trash-list-header">
        <div className="trash-list-header-left">
          <input
            type="checkbox"
            checked={selected.length === trashData.length && trashData.length > 0}
            onChange={handleSelectAll}
          />
          <span>Chọn tất cả ({trashData.length} mục)</span>
        </div>
        <div className="trash-list-header-right">Hiển thị {trashData.length}/{trashData.length} mục</div>
      </div>
      <div className="trash-list-content">
        {trashData.map((item, idx) => (
        <div className="trash-list-item" key={item._id || item.id}>
          <input
            type="checkbox"
            className="trash-list-checkbox"
            checked={selected.includes(item._id || item.id)}
            onChange={() => handleSelect(item._id || item.id)}
          />
          <div className="trash-list-main">
            <div className="trash-list-title">
              <span className="trash-list-icon">
                <img src={getIconByType(item.type)} alt={item.type} />
              </span>
              <span>{item.title || item.name}</span>
              <span className="trash-list-type">{item.type}</span>
              {/* Với dự án: luôn có thể khôi phục, chỉ hiển thị cảnh báo cho công việc không thể khôi phục */}
              {!item.canRestore && item.type !== 'Dự án' && (
                <span style={{ 
                  color: '#e74c3c', 
                  fontSize: '12px', 
                  marginLeft: '8px',
                  backgroundColor: '#fdf2f2',
                  padding: '2px 6px',
                  borderRadius: '4px'
                }}>
                  Không thể khôi phục vì công việc chính đã bị xóa
                </span>
              )}
            </div>
            <div className="trash-list-info">
              {/* Hiển thị thông tin project cho công việc chính */}
              {!item.parentTaskId && item.projectInfo && (
                <div>
                  Dự án: <b>{item.projectInfo.name} ({item.projectInfo.projectCode})</b>
                </div>
              )}
              {/* Hiển thị mã dự án nếu có */}
              {item.code && <div>Mã dự án: <b>{item.code}</b></div>}
              {item.desc && <div>{item.desc}</div>}
              {/* Hiển thị thông tin task cha cho công việc phụ */}
              {item.parentTaskInfo && (
                <div>
                  Công việc chính: <b>{item.parentTaskInfo.taskCode} - {item.parentTaskInfo.title}</b>
                </div>
              )}
            </div>
            <div className="trash-list-info trash-list-info-2">
              {item.deletedDate && <div>Ngày xóa <b>{item.deletedDate}</b></div>}
              {item.deletedBy && (
                <div>
                  Người xóa{" "}
                  <b>
                    {typeof item.deletedBy === "object"
                      ? `${item.deletedBy.fullName}${item.deletedBy.employeeCode ? ` (${item.deletedBy.employeeCode})` : ''}`
                      : item.deletedBy}
                  </b>
                </div>
              )}
              {item.createdBy && (
                <div>
                  Người tạo{" "}
                  <b>
                    {typeof item.createdBy === "object"
                      ? `${item.createdBy.fullName}${item.createdBy.employeeCode ? ` (${item.createdBy.employeeCode})` : ''}`
                      : item.createdBy}
                  </b>
                </div>
              )}
              {item.leader && (
                <div>
                  Leader{" "}
                  <b>
                    {typeof item.leader === "object"
                      ? `${item.leader.fullName}${item.leader.employeeCode ? ` (${item.leader.employeeCode})` : ''}`
                      : item.leader}
                  </b>
                </div>
              )}
              {/* Hiển thị người được giao - ưu tiên assignedToIds nếu có */}
              {item.assignedToIds && item.assignedToIds.length > 0 ? (
                <div>
                  Người được giao{" "}
                  <b>
                    {item.assignedToIds.map((user, index) => 
                      typeof user === "object"
                        ? `${user.fullName}${user.employeeCode ? ` (${user.employeeCode})` : ''}`
                        : user
                    ).join(", ")}
                  </b>
                </div>
              ) : item.assignedTo && (
                <div>
                  Người được giao{" "}
                  <b>
                    {typeof item.assignedTo === "object"
                      ? `${item.assignedTo.fullName}${item.assignedTo.employeeCode ? ` (${item.assignedTo.employeeCode})` : ''}`
                      : item.assignedTo}
                  </b>
                </div>
              )}
              {item.deadline && <div>Deadline <b>{item.deadline}</b></div>}
            </div>
            {item.members && (
              <div className="trash-list-members">
                {item.members.map((m, i) => (
                  <span className="trash-list-member" key={i}>{m}</span>
                ))}
              </div>
            )}
          </div>
          <div className="trash-list-actions">
            <span className="trash-list-type">{item.type}</span>
            <button className="trash-list-more" onClick={() => setActionIdx(idx)}>⋯</button>
            {actionIdx === idx && (
              <ActionTrash
                onRestore={() => handleRestoreItem(item)}
                onDelete={() => handleDeleteItem(item)}
                onClose={() => setActionIdx(null)}
                canRestore={item.canRestore}
              />
            )}
          </div>
        </div>
      ))}

        {/* Show loading indicator at bottom if still loading but have data */}
        {loading && trashData.length > 0 && (
          <></>
        )}
      </div>
    </div>
  );
};

export default All;
