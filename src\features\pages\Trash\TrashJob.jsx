// Cache và preload để tăng tốc độ loading
let jobTrashCache = null;
let jobCacheTimestamp = 0;
const JOB_CACHE_DURATION = 10000; // 10 giây
let jobPreloadPromise = null;

import ActionTrash from "../../components/TrashAction";
import TrashActionBar from "../../components/TrashActionBar";
import React, { useState, useEffect } from "react";
import "../../../styles/TrashList.css";
import "../../../styles/Trash.css";
import SquareCheckIcon from "../../../assets/square-check-big.svg";
import {
  getDeletedProjectTasks,
  restoreProjectTask,
  permanentDeleteProjectTask,
} from "../../../api/taskManagement";
import {
  getDeletedPersonalTasks,
  restorePersonalTask,
  permanentDeletePersonalTask,
} from "../../../api/taskManagement";
import { getAllProjects } from "../../../api/projectManagement";
import { showSuccess, showError } from "../../../utils/toastUtils";

const statusColor = (status) => {
  if (status === "Hoàn thành") return { color: "#27ae60" };
  if (status === "Đang chờ") return { color: "#e67e22" };
  return {};
};

const Job = ({ onDataChange }) => {
  const [jobData, setJobData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [actionIdx, setActionIdx] = useState(null);
  const [selectedIds, setSelectedIds] = useState([]);
  const [lastStats, setLastStats] = useState(null);

  // Gửi thống kê lên cha
  const sendStatsToParent = (jobCount, selectedCount) => {
    if (onDataChange) {
      const stats = {
        totalItems: jobCount,
        projectCount: 0,
        jobCount: jobCount,
        userCount: 0,
        selectedCount: selectedCount,
      };
      if (JSON.stringify(stats) !== JSON.stringify(lastStats)) {
        setLastStats(stats);
        onDataChange(stats);
      }
    }
  };

  // Preload function
  const preloadJobData = async () => {
    if (jobPreloadPromise) return jobPreloadPromise;

    jobPreloadPromise = (async () => {
      try {
        // Lấy tất cả projectId
        let allProjectIds = [];
        let projectsMap = {};
        try {
          const projectsRes = await getAllProjects();
          const projects = projectsRes.data || projectsRes || [];
          allProjectIds = projects.map((p) => p._id || p.id);
          
          // Tạo map project để kiểm tra trạng thái
          projects.forEach(project => {
            projectsMap[project._id || project.id] = {
              name: project.name || 'Không có tên',
              projectCode: project.projectCode || 'N/A',
              isDeleted: project.isDeleted || false
            };
          });
        } catch (err) {
          allProjectIds = [];
        }
        let allTasks = [];
        if (allProjectIds.length > 0) {

          // Lấy task đã xóa cho từng project
          for (const projectId of allProjectIds) {
            try {
              const res = await getDeletedProjectTasks(projectId);
              const data = res.data || res || [];
              // Thêm thông tin project vào mỗi task
              const tasksWithProjectId = data.map((task) => {
                // Xác định subtask chính xác hơn - kiểm tra cả parentTaskId và taskCode
                const isSubtask = task.parentTaskId && (
                  (task.parentTaskId._id || task.parentTaskId.id || task.parentTaskId) ||
                  (typeof task.parentTaskId === 'object' && task.parentTaskId !== null && Object.keys(task.parentTaskId).length > 0)
                ) || (task.taskCode && task.taskCode.startsWith('SUB-'));
                

                
                // Tạo parentTaskInfo cho subtask - cải thiện logic để đảm bảo luôn có thông tin
                let parentTaskInfo = null;
                if (isSubtask) {
                  if (task.parentTaskId && typeof task.parentTaskId === 'object' && task.parentTaskId._id) {
                    // Nếu parentTaskId được populate đầy đủ
                    parentTaskInfo = {
                      title: task.parentTaskId.title || task.parentTaskId.name || 'Không có tiêu đề',
                      taskCode: task.parentTaskId.taskCode || task.parentTaskId.code || 'N/A',
                      id: task.parentTaskId._id || task.parentTaskId.id || task.parentTaskId,
                      isDeleted: task.parentTaskId.isDeleted || false
                    };
                  } else if (task.parentTaskId && typeof task.parentTaskId === 'string') {
                    // Nếu parentTaskId chỉ là ID string, cần tìm task chính trong cùng project
                    // Tìm trong data hiện tại trước
                    const parentTask = data.find(t => 
                      (t._id || t.id) === task.parentTaskId || 
                      (t.taskCode && t.taskCode === task.parentTaskId)
                    );
                    if (parentTask) {
                      parentTaskInfo = {
                        title: parentTask.title || parentTask.name || 'Không có tiêu đề',
                        taskCode: parentTask.taskCode || parentTask.code || 'N/A',
                        id: parentTask._id || parentTask.id,
                        isDeleted: parentTask.isDeleted || false
                      };
                    }
                  }
                  
                  // Nếu vẫn không có parentTaskInfo, tạo thông tin mặc định
                  if (!parentTaskInfo) {
                    parentTaskInfo = {
                      title: 'Không thể xác định [Đã bị xóa]',
                      taskCode: 'N/A',
                      id: task.parentTaskId || 'unknown',
                      isDeleted: true // Giả sử đã bị xóa nếu không tìm thấy
                    };
                  }
                }
                

                
                // Cập nhật logic canRestore để kiểm tra chính xác hơn
                const parentTaskDeleted = parentTaskInfo ? parentTaskInfo.isDeleted : false;
                const canRestore = !projectsMap[projectId]?.isDeleted && (!isSubtask || !parentTaskDeleted);
                const taskType = isSubtask ? "Công việc phụ" : "Công việc";
                
                return {
                  ...task,
                  projectId: projectId,
                    // Thêm thông tin project nếu có
                  projectInfo: projectsMap[projectId] || { name: 'Không có tên', projectCode: 'N/A', isDeleted: false },
                  // Đảm bảo có các trường cần thiết
                  title: task.title || task.name || task.taskCode || 'Không có tiêu đề',
                  // Xác định loại task chính xác hơn
                  type: taskType,
                  // Thêm thông tin ngày tháng
                  deletedDate: task.deletedAt ? new Date(task.deletedAt).toLocaleDateString('vi-VN') : 'Không xác định',
                  deadline: task.dueDate ? new Date(task.dueDate).toLocaleDateString('vi-VN') : 'Không có deadline',
                  // Thêm thông tin task cha cho subtask
                  parentTaskInfo: parentTaskInfo,
                  // Thêm flag để kiểm tra có thể khôi phục hay không
                  canRestore: canRestore
                };
              });
              allTasks = allTasks.concat(tasksWithProjectId);
            } catch (err) {
              // Log lỗi chi tiết hơn để debug
              console.warn(`Error fetching deleted tasks for project ${projectId}:`, err);
              console.warn('Error details:', {
                projectId,
                error: err.message,
                response: err.response?.data
              });
            }
          }
        } else {
          // Nếu không có project nào, lấy task cá nhân đã xóa
          try {
            const res = await getDeletedPersonalTasks();
            const data = res.data || res || [];
            allTasks = allTasks.concat(data);
          } catch (err) {}
        }
        // Lọc theo quyền
        const userRaw = JSON.parse(localStorage.getItem("user") || "{}");
        const role =
          userRaw.user?.role?.toLowerCase() ||
          userRaw.role?.toLowerCase() ||
          "staff";
        const userName =
          userRaw.user?.fullName || userRaw.fullName || userRaw.name;
        let filteredTasks = allTasks;
        if (role !== "admin" && role !== "ceo") {
          filteredTasks = (allTasks || []).filter(
            (item) => item.deletedBy === userName
          );
        }

        jobTrashCache = filteredTasks;
        jobCacheTimestamp = Date.now();

        return filteredTasks;
      } catch (err) {
        console.error("Error in preloadJobData:", err);
        return [];
      }
    })();

    return jobPreloadPromise;
  };

  useEffect(() => {
    const fetchJobs = async () => {
      // Kiểm tra cache trước
      const now = Date.now();
      if (jobTrashCache && now - jobCacheTimestamp < JOB_CACHE_DURATION) {
        setJobData(jobTrashCache);
        sendStatsToParent(jobTrashCache.length, selectedIds.length);
        setLoading(false);
        return;
      }

      // Nếu không có cache, mới set loading=true
      setLoading(true);
      setError(null);

      try {
        let filteredTasks;
        if (jobPreloadPromise) {
          filteredTasks = await jobPreloadPromise;
        } else {
          filteredTasks = await preloadJobData();
        }

        setJobData(filteredTasks);
        sendStatsToParent(filteredTasks.length, selectedIds.length);
      } catch (err) {
        setError("Lỗi khi tải danh sách công việc đã xóa");
      } finally {
        setLoading(false);
      }
    };
    fetchJobs();
  }, []);

  // Start preloading when component mounts
  useEffect(() => {
    preloadJobData();
  }, []);

  // Gửi lại stats khi chọn checkbox
  useEffect(() => {
    sendStatsToParent(jobData.length, selectedIds.length);
  }, [jobData.length, selectedIds.length]);

  const handleRestoreItem = async (id) => {
    try {
      const task = jobData.find((item) => (item._id || item.id) === id);
      
      // Kiểm tra logic khôi phục trước khi thực hiện
      if (!task.canRestore) {
        if (task.projectInfo?.isDeleted) {
          showError("Không thể khôi phục công việc vì dự án đã bị xóa!");
          return;
        }
        if (task.type === "Công việc phụ" && task.parentTaskInfo?.isDeleted) {
          showError("Không thể khôi phục công việc phụ vì công việc chính đã bị xóa!");
          return;
        }
      }
      
      if (task && task.projectId) {
        await restoreProjectTask(task.projectId, id);
      } else {
        await restorePersonalTask(id);
      }
      setJobData((prev) => prev.filter((item) => (item._id || item.id) !== id));
      setActionIdx(null);
      showSuccess("Khôi phục công việc thành công!");
    } catch (err) {
      showError("Lỗi khi khôi phục công việc: " + (err.message || err));
      setActionIdx(null);
    }
  };
  const handleDeleteItem = async (id) => {
    try {
      const task = jobData.find((item) => (item._id || item.id) === id);
      if (task && task.projectId) {
        await permanentDeleteProjectTask(task.projectId, id);
      } else {
        await permanentDeletePersonalTask(id);
      }
      setJobData((prev) => prev.filter((item) => (item._id || item.id) !== id));
      setActionIdx(null);
      showSuccess("Xóa vĩnh viễn công việc thành công!");
    } catch (err) {
      showError("Lỗi khi xóa vĩnh viễn công việc: " + (err.message || err));
      setActionIdx(null);
    }
  };
  const handleSelect = (id) => {
    setSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((sid) => sid !== id) : [...prev, id]
    );
  };
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      setSelectedIds(jobData.map((item) => item._id || item.id));
    } else {
      setSelectedIds([]);
    }
  };
  const handleClearSelection = () => setSelectedIds([]);
  const handleRestoreSelected = async () => {
    const itemsToRestore = selectedIds.map(id => jobData.find(i => (i._id || i.id) === id)).filter(Boolean);
    
    // Kiểm tra logic khôi phục trước khi thực hiện
    const mainTasks = itemsToRestore.filter(item => item.type === "Công việc");
    const subTasks = itemsToRestore.filter(item => item.type === "Công việc phụ");
    
    // Kiểm tra xem có task phụ nào mà task chính không được chọn khôi phục hoặc đã bị xóa không
    const mainTaskIds = mainTasks.map(task => task._id || task.id);
    const subTasksWithoutParent = subTasks.filter(subTask => 
      !mainTaskIds.includes(subTask.parentTaskInfo?.id)
    );
    
    if (subTasksWithoutParent.length > 0) {
      const taskNames = subTasksWithoutParent.map(task => task.title).join(', ');
      showError(`Không thể khôi phục các công việc phụ: ${taskNames}. Vui lòng khôi phục công việc chính trước.`);
      return;
    }
    
    // Kiểm tra xem có task phụ nào mà task chính đã bị xóa không
    const subTasksWithDeletedParent = subTasks.filter(subTask => 
      subTask.parentTaskInfo?.isDeleted
    );
    
    if (subTasksWithDeletedParent.length > 0) {
      const taskNames = subTasksWithDeletedParent.map(task => task.title).join(', ');
      showError(`Không thể khôi phục các công việc: ${taskNames}. Công việc chính đã bị xóa.`);
      return;
    }
    
    // Kiểm tra xem có task nào thuộc project đã bị xóa không
    const tasksInDeletedProjects = itemsToRestore.filter(task => task.projectInfo?.isDeleted);
    if (tasksInDeletedProjects.length > 0) {
      const taskNames = tasksInDeletedProjects.map(task => task.title).join(', ');
      showError(`Không thể khôi phục các công việc: ${taskNames}. Dự án đã bị xóa.`);
      return;
    }
    
    // Thực hiện khôi phục từng item một để xử lý lỗi riêng lẻ
    const results = [];
    const errors = [];
    
    for (const item of itemsToRestore) {
      try {
        // Kiểm tra xem task có thể khôi phục không
        if (!item.canRestore) {
          if (item.projectInfo?.isDeleted) {
            errors.push({ item, error: "Không thể khôi phục vì dự án đã bị xóa" });
            continue;
          }
          if (item.type === "Công việc phụ" && item.parentTaskInfo?.isDeleted) {
            errors.push({ item, error: "Không thể khôi phục vì công việc chính đã bị xóa" });
            continue;
          }
        }
        
        if (item.projectId) {
          await restoreProjectTask(item.projectId, item._id || item.id);
        } else {
          await restorePersonalTask(item._id || item.id);
        }
        results.push(item);
      } catch (err) {
        errors.push({ item, error: err.message || err });
      }
    }
    
    // Cập nhật UI
    if (results.length > 0) {
      setJobData(prev => prev.filter(item => !results.some(r => (r._id || r.id) === (item._id || item.id))));
      setSelectedIds([]);
      showSuccess(`Khôi phục thành công ${results.length} mục!`);
    }
    
    if (errors.length > 0) {
      const errorMessages = errors.map(e => `${e.item.title}: ${e.error}`).join('\n');
      showError(`Lỗi khi khôi phục ${errors.length} mục:\n${errorMessages}`);
    }
  };
  const handleDeleteSelected = async () => {
    const itemsToDelete = selectedIds.map(id => jobData.find(i => (i._id || i.id) === id)).filter(Boolean);
    const promises = itemsToDelete.map(item => {
      if (item.projectId) return permanentDeleteProjectTask(item.projectId, item._id || item.id);
      return permanentDeletePersonalTask(item._id || item.id);
    });

    try {
      await Promise.all(promises);
      setJobData(prev => prev.filter(item => !selectedIds.includes(item._id || item.id)));
      setSelectedIds([]);
      showSuccess("Xóa vĩnh viễn hàng loạt thành công!");
    } catch (err) {
      showError("Lỗi khi xóa vĩnh viễn hàng loạt: " + (err.message || err));
    }
  };

  // Skeleton loading component
  const JobSkeleton = () => (
    <div className="trash-skeleton-container">
      {[1, 2, 3, 4, 5, 6].map((idx) => (
        <div key={idx} className="trash-skeleton-item">
          <div className="trash-skeleton-checkbox"></div>
          <div className="trash-skeleton-main">
            <div className="trash-skeleton-title-row">
              <div className="trash-skeleton-icon"></div>
              <div className="trash-skeleton-title"></div>
              <div className="trash-skeleton-type"></div>
            </div>
            <div className="trash-skeleton-info">
              <div className="trash-skeleton-text"></div>
              <div className="trash-skeleton-text"></div>
              <div className="trash-skeleton-text"></div>
            </div>
            <div className="trash-skeleton-members">
              <div className="trash-skeleton-member"></div>
              <div className="trash-skeleton-member"></div>
            </div>
          </div>
          <div className="trash-skeleton-actions">
            <div className="trash-skeleton-action-type"></div>
            <div className="trash-skeleton-more-btn"></div>
          </div>
        </div>
      ))}
    </div>
  );

  if (loading && jobData.length === 0) {
    return (
      <div className="trash-list-container">
        <div className="trash-list-header">
          <div className="trash-list-header-left">
            <input type="checkbox" disabled style={{ opacity: 0.5 }} />
            <span style={{ color: "#666" }}>Đang tải công việc...</span>
          </div>
          <div className="trash-list-header-right" style={{ color: "#666" }}>
            Đang tải...
          </div>
        </div>
        <JobSkeleton />
      </div>
    );
  }

  if (error)
    return (
      <div
        style={{
          color: "red",
          textAlign: "center",
          padding: "40px",
          background: "#fff",
          borderRadius: "8px",
          margin: "20px 0",
        }}
      >
        <div>❌ {error}</div>
        <button
          onClick={() => window.location.reload()}
          style={{
            marginTop: "10px",
            padding: "8px 16px",
            background: "#007bff",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
          }}
        >
          Thử lại
        </button>
      </div>
    );

  return (
    <div className="trash-list-container">
      {selectedIds.length > 0 && (
        <TrashActionBar
          selectedCount={selectedIds.length}
          onRestore={handleRestoreSelected}
          onDelete={handleDeleteSelected}
          onClearSelection={handleClearSelection}
        />
      )}
      <div className="trash-list-header">
        <div className="trash-list-header-left">
          <input
            type="checkbox"
            checked={selectedIds.length === jobData.length}
            onChange={handleSelectAll}
          />
          <span>Chọn tất cả ({jobData.length} mục)</span>
        </div>
        <div className="trash-list-header-right">
          Hiển thị {jobData.length}/{jobData.length} mục
        </div>
      </div>
      <div className="trash-list-content">
        {jobData.map((item, idx) => (
          <div className="trash-list-item" key={item._id || item.id}>
            <input
              type="checkbox"
              className="trash-list-checkbox"
              checked={selectedIds.includes(item._id || item.id)}
              onChange={() => handleSelect(item._id || item.id)}
            />
            <div className="trash-list-main">
              <div className="trash-list-title">
                <span className="trash-list-icon">
                  <img src={SquareCheckIcon} alt={item.type || 'Công việc'} />
                </span>
                <span>{item.title || item.name}</span>
                <span className="trash-list-type">{item.type || "Công việc"}</span>
                {!item.canRestore && (
                  <span style={{ 
                    color: '#e74c3c', 
                    fontSize: '12px', 
                    marginLeft: '8px',
                    backgroundColor: '#fdf2f2',
                    padding: '2px 6px',
                    borderRadius: '4px'
                  }}>
                    Không thể khôi phục vì công việc chính đã bị xóa
                  </span>
                )}
              </div>
              <div className="trash-list-info">
                {/* Hiển thị thông tin project cho tất cả công việc */}
                {item.projectInfo && (
                  <div>
                    Dự án: <b>{item.projectInfo.name} ({item.projectInfo.projectCode})</b>
                    {item.projectInfo.isDeleted && (
                      <span style={{ color: '#e74c3c', marginLeft: '8px' }}>
                        [Đã bị xóa]
                      </span>
                    )}
                  </div>
                )}
                {/* Hiển thị mã dự án nếu có */}
                {item.code && (
                  <div>
                    Mã dự án: <b>{item.code}</b>
                  </div>
                )}
                {item.desc && <div>{item.desc}</div>}
                {/* Hiển thị thông tin task cha cho công việc phụ */}
                {item.parentTaskInfo && (
                  <div>
                    Công việc chính: <b>{item.parentTaskInfo.taskCode} - {item.parentTaskInfo.title}</b>
                  </div>
                )}
              </div>
              <div className="trash-list-info trash-list-info-2">
                {item.deletedDate && (
                  <div>
                    Ngày xóa <b>{item.deletedDate}</b>
                  </div>
                )}
                {item.deadline && (
                  <div>
                    Deadline <b>{item.deadline}</b>
                  </div>
                )}
                {item.deletedBy && (
                  <div>
                    Người xóa{" "}
                    <b>
                      {typeof item.deletedBy === "object"
                        ? `${item.deletedBy.fullName}${item.deletedBy.employeeCode ? ` (${item.deletedBy.employeeCode})` : ''}`
                        : item.deletedBy}
                    </b>
                  </div>
                )}
                {item.createdBy && (
                  <div>
                    Người tạo{" "}
                    <b>
                      {typeof item.createdBy === "object"
                        ? `${item.createdBy.fullName}${item.createdBy.employeeCode ? ` (${item.createdBy.employeeCode})` : ''}`
                        : item.createdBy}
                    </b>
                  </div>
                )}
                {/* Hiển thị người được giao - ưu tiên assignedToIds nếu có */}
                {item.assignedToIds && item.assignedToIds.length > 0 ? (
                  <div>
                    Người được giao{" "}
                    <b>
                      {item.assignedToIds.map((user, index) => 
                        typeof user === "object"
                          ? `${user.fullName}${user.employeeCode ? ` (${user.employeeCode})` : ''}`
                          : user
                      ).join(", ")}
                    </b>
                  </div>
                ) : item.assignedTo && (
                  <div>
                    Người được giao{" "}
                    <b>
                      {typeof item.assignedTo === "object"
                        ? `${item.assignedTo.fullName}${item.assignedTo.employeeCode ? ` (${item.assignedTo.employeeCode})` : ''}`
                        : item.assignedTo}
                    </b>
                  </div>
                )}
              </div>
              {item.members && (
                <div className="trash-list-members">
                  {item.members.map((m, i) => (
                    <span className="trash-list-member" key={i}>
                      {m}
                    </span>
                  ))}
                </div>
              )}
            </div>
            <div className="trash-list-actions">
              <span className="trash-list-type">{item.type || "Công việc"}</span>
              <button
                className="trash-list-more"
                onClick={() => setActionIdx(idx)}
              >
                ⋯
              </button>
              {actionIdx === idx && (
                <ActionTrash
                  onRestore={() => handleRestoreItem(item._id || item.id)}
                  onDelete={() => handleDeleteItem(item._id || item.id)}
                  onClose={() => setActionIdx(null)}
                  canRestore={item.canRestore}
                />
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Job;
