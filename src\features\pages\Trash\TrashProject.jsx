// Cache và preload để tăng tốc độ loading
let projectTrashCache = null;
let projectCacheTimestamp = 0;
const PROJECT_CACHE_DURATION = 10000; // 10 giây
let projectPreloadPromise = null;

import ActionTrash from "../../components/TrashAction";
import TrashActionBar from "../../components/TrashActionBar";
import React, { useState, useEffect } from "react";
import "../../../styles/TrashList.css";
import "../../../styles/Trash.css";
import FolderIcon from "../../../assets/file-text.svg";
import { getDeletedProjects, restoreProject, permanentDeleteProject } from "../../../api/projectManagement";
import { showSuccess, showError } from "../../../utils/toastUtils";

const statusColor = (status) => {
  if (status === "<PERSON><PERSON><PERSON> thành") return { color: '#27ae60' };
  if (status === "Đang chờ") return { color: '#e67e22' };
  return {};
};

const Project = ({ onDataChange }) => {
  const [projectData, setProjectData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [actionIdx, setActionIdx] = useState(null);
  const [selected, setSelected] = useState([]);
  const [lastStats, setLastStats] = useState(null);

  // Gửi thống kê lên cha
  const sendStatsToParent = (projectCount, selectedCount) => {
    if (onDataChange) {
      const stats = {
        totalItems: projectCount,
        projectCount: projectCount,
        jobCount: 0,
        userCount: 0,
        selectedCount: selectedCount
      };
      if (JSON.stringify(stats) !== JSON.stringify(lastStats)) {
        setLastStats(stats);
        onDataChange(stats);
      }
    }
  };

  // Preload function
  const preloadProjectData = async () => {
    if (projectPreloadPromise) return projectPreloadPromise;

    projectPreloadPromise = (async () => {
      try {
        const res = await getDeletedProjects();
        let data = res.data || res;
        
        // Format dữ liệu ngày tháng
        data = (data || []).map(item => ({
          ...item,
          deletedDate: item.deletedAt ? new Date(item.deletedAt).toLocaleDateString('vi-VN') : 'Không xác định',
          deadline: item.endDate ? new Date(item.endDate).toLocaleDateString('vi-VN') : 'Không có deadline'
        }));
        
        // Lọc theo quyền
        const userRaw = JSON.parse(localStorage.getItem('user') || '{}');
        const role = userRaw.user?.role?.toLowerCase() || userRaw.role?.toLowerCase() || 'staff';
        const userId = userRaw.user?._id || userRaw._id || userRaw.user?.id || userRaw.id;
        const userDeptId = userRaw.user?.departmentId || userRaw.departmentId || userRaw.user?.department?._id || userRaw.department?._id || userRaw.user?.department || userRaw.department;
        if (role === 'admin' || role === 'ceo') {
          // show all
        } else if (role === 'departmenthead') {
          data = (data || []).filter(item => {
            const projectDeptId = item.departmentId?._id || item.departmentId || item.department?._id || item.department;
            return projectDeptId && userDeptId && projectDeptId.toString() === userDeptId.toString();
          });
        } else if (role === 'leader') {
          data = (data || []).filter(item => {
            const projectLeaderId = item.leaderId?._id || item.leaderId || item.leader?._id || item.leader;
            return projectLeaderId && userId && projectLeaderId.toString() === userId.toString();
          });
        } else {
          data = (data || []).filter(item => item.deletedBy === (userRaw.user?.fullName || userRaw.fullName || userRaw.name));
        }

        projectTrashCache = data;
        projectCacheTimestamp = Date.now();

        return data;
      } catch (err) {
        console.error("Error in preloadProjectData:", err);
        return [];
      }
    })();

    return projectPreloadPromise;
  };

  useEffect(() => {
    const fetchProjects = async () => {
      // Kiểm tra cache trước
      const now = Date.now();
      if (projectTrashCache && (now - projectCacheTimestamp) < PROJECT_CACHE_DURATION) {
        setProjectData(projectTrashCache);
        sendStatsToParent(projectTrashCache.length, selected.length);
        setLoading(false);
        return;
      }

      // Nếu không có cache, mới set loading=true
      setLoading(true);
      setError(null);

      try {
        let data;
        if (projectPreloadPromise) {
          data = await projectPreloadPromise;
        } else {
          data = await preloadProjectData();
        }

        setProjectData(data);
        sendStatsToParent(data.length, selected.length);
      } catch (err) {
        setError("Lỗi khi tải danh sách dự án đã xóa");
      } finally {
        setLoading(false);
      }
    };
    fetchProjects();
  }, []);

  // Start preloading when component mounts
  useEffect(() => {
    preloadProjectData();
  }, []);

  // Gửi lại stats khi chọn checkbox
  useEffect(() => {
    sendStatsToParent(projectData.length, selected.length);
  }, [projectData.length, selected.length]);

  const handleRestoreItem = async (id) => {
    try {
      await restoreProject(id);
      setProjectData(prev => prev.filter(item => (item._id || item.id) !== id));
      setActionIdx(null);
      showSuccess("Khôi phục dự án thành công!");
    } catch (err) {
      showError("Lỗi khi khôi phục dự án: " + (err.message || err));
      setActionIdx(null);
    }
  };
  const handleDeleteItem = async (id) => {
    try {
      await permanentDeleteProject(id);
      setProjectData(prev => prev.filter(item => (item._id || item.id) !== id));
      setActionIdx(null);
      showSuccess("Xóa vĩnh viễn dự án thành công!");
    } catch (err) {
      showError("Lỗi khi xóa vĩnh viễn dự án: " + (err.message || err));
      setActionIdx(null);
    }
  };
  const handleSelect = (id) => {
    setSelected((prev) =>
      prev.includes(id) ? prev.filter((sid) => sid !== id) : [...prev, id]
    );
  };
  const handleSelectAll = () => {
    if (selected.length === projectData.length) {
      setSelected([]);
    } else {
      setSelected(projectData.map((item) => item._id || item.id));
    }
  };
  const handleClearSelection = () => setSelected([]);

  // Xử lý xóa vĩnh viễn nhiều mục
  const handleDeleteSelected = async () => {
    const promises = selected.map(id => permanentDeleteProject(id));
    try {
      await Promise.all(promises);
      setProjectData(prev => prev.filter(item => !selected.includes(item._id || item.id)));
      setSelected([]);
      showSuccess("Xóa vĩnh viễn hàng loạt thành công!");
    } catch (err) {
      showError("Lỗi khi xóa vĩnh viễn hàng loạt: " + (err.message || err));
    }
  };
  // Xử lý khôi phục nhiều mục
  const handleRestoreSelected = async () => {
    const promises = selected.map(id => restoreProject(id));
    try {
      await Promise.all(promises);
      setProjectData(prev => prev.filter(item => !selected.includes(item._id || item.id)));
      setSelected([]);
      showSuccess("Khôi phục hàng loạt thành công!");
    } catch (err) {
      showError("Lỗi khi khôi phục hàng loạt: " + (err.message || err));
    }
  };

  // Skeleton loading component
  const ProjectSkeleton = () => (
    <div className="trash-skeleton-container">
      {[1, 2, 3, 4, 5, 6].map((idx) => (
        <div key={idx} className="trash-skeleton-item">
          <div className="trash-skeleton-checkbox"></div>
          <div className="trash-skeleton-main">
            <div className="trash-skeleton-title-row">
              <div className="trash-skeleton-icon"></div>
              <div className="trash-skeleton-title"></div>
              <div className="trash-skeleton-type"></div>
            </div>
            <div className="trash-skeleton-info">
              <div className="trash-skeleton-text"></div>
              <div className="trash-skeleton-text"></div>
              <div className="trash-skeleton-text"></div>
            </div>
            <div className="trash-skeleton-members">
              <div className="trash-skeleton-member"></div>
              <div className="trash-skeleton-member"></div>
              <div className="trash-skeleton-member"></div>
              <div className="trash-skeleton-member"></div>
            </div>
          </div>
          <div className="trash-skeleton-actions">
            <div className="trash-skeleton-action-type"></div>
            <div className="trash-skeleton-more-btn"></div>
          </div>
        </div>
      ))}
    </div>
  );

  if (loading && projectData.length === 0) {
    return (
      <div className="trash-list-container">
        <div className="trash-list-header">
          <div className="trash-list-header-left">
            <input type="checkbox" disabled style={{ opacity: 0.5 }} />
            <span style={{ color: '#666' }}>Đang tải dự án...</span>
          </div>
          <div className="trash-list-header-right" style={{ color: '#666' }}>Đang tải...</div>
        </div>
        <ProjectSkeleton />
      </div>
    );
  }

  if (error) return (
    <div style={{
      color: "red",
      textAlign: "center",
      padding: "40px",
      background: "#fff",
      borderRadius: "8px",
      margin: "20px 0"
    }}>
      <div>❌ {error}</div>
      <button
        onClick={() => window.location.reload()}
        style={{
          marginTop: '10px',
          padding: '8px 16px',
          background: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        Thử lại
      </button>
    </div>
  );

  return (
    <div className="trash-list-container">
      {selected.length > 0 && (
        <TrashActionBar
          selectedCount={selected.length}
          onRestore={handleRestoreSelected}
          onDelete={handleDeleteSelected}
          onClearSelection={handleClearSelection}
        />
      )}
      <div className="trash-list-header">
        <div className="trash-list-header-left">
          <input
            type="checkbox"
            checked={selected.length === projectData.length && projectData.length > 0}
            onChange={handleSelectAll}
          />
          <span>Chọn tất cả ({projectData.length} dự án)</span>
        </div>
        <div className="trash-list-header-right">Hiển thị {projectData.length}/{projectData.length} dự án</div>
      </div>
      <div className="trash-list-content">
        {projectData.map((item, idx) => (
        <div className="trash-list-item" key={item._id || item.id}>
          <input
            type="checkbox"
            className="trash-list-checkbox"
            checked={selected.includes(item._id || item.id)}
            onChange={() => handleSelect(item._id || item.id)}
          />
          <div className="trash-list-main">
            <div className="trash-list-title">
              <span className="trash-list-icon">
                <img src={FolderIcon} alt={item.type || 'Dự án'} />
              </span>
              <span>{item.title || item.name}</span>
              <span className="trash-list-type">Dự án</span>
            </div>
            <div className="trash-list-info">
              {item.code && <div>Mã dự án: <b>{item.code}</b></div>}
              {item.desc && <div>{item.desc}</div>}
            </div>
            <div className="trash-list-info trash-list-info-2">
              {item.deletedDate && <div>Ngày xóa <b>{item.deletedDate}</b></div>}
              {item.deletedBy && (
                <div>
                  Người xóa{" "}
                  <b>
                    {typeof item.deletedBy === "object"
                      ? `${item.deletedBy.fullName}${item.deletedBy.employeeCode ? ` (${item.deletedBy.employeeCode})` : ''}`
                      : item.deletedBy}
                  </b>
                </div>
              )}
              {item.createdBy && (
                <div>
                  Người tạo{" "}
                  <b>
                    {typeof item.createdBy === "object"
                      ? `${item.createdBy.fullName}${item.createdBy.employeeCode ? ` (${item.createdBy.employeeCode})` : ''}`
                      : item.createdBy}
                  </b>
                </div>
              )}
              {item.leader && (
                <div>
                  Leader{" "}
                  <b>
                    {typeof item.leader === "object"
                      ? `${item.leader.fullName}${item.leader.employeeCode ? ` (${item.leader.employeeCode})` : ''}`
                      : item.leader}
                  </b>
                </div>
              )}
              {item.deadline && <div>Deadline <b>{item.deadline}</b></div>}
            </div>
            {item.members && (
              <div className="trash-list-members">
                {item.members.map((m, i) => (
                  <span className="trash-list-member" key={i}>{m}</span>
                ))}
              </div>
            )}
          </div>
          <div className="trash-list-actions">
            <span className="trash-list-type">Dự án</span>
            <button className="trash-list-more" onClick={() => setActionIdx(idx)}>⋯</button>
            {actionIdx === idx && (
              <ActionTrash
                onRestore={() => handleRestoreItem(item._id || item.id)}
                onDelete={() => handleDeleteItem(item._id || item.id)}
                onClose={() => setActionIdx(null)}
              />
            )}
          </div>
        </div>
        ))}
      </div>
    </div>
  );
};

export default Project;
